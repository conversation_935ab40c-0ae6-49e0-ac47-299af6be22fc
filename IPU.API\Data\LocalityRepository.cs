using IPU.API.Models;
using Microsoft.Data.SqlClient;
using System.Data;

namespace IPU.API.Data
{
    /// <summary>
    /// Repository implementation for locality operations using stored procedures
    /// </summary>
    public class LocalityRepository : ILocalityRepository
    {
        private readonly string _connectionString;
        private readonly ILogger<LocalityRepository> _logger;

        public LocalityRepository(IConfiguration configuration, ILogger<LocalityRepository> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        public async Task<List<Locality>> GetAllLocalitiesByTypeAsync(int localityTypeId)
        {
            var localities = new List<Locality>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetLocalitiesByType]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                localities.Add(MapLocality(reader));
            }

            return localities;
        }

        public async Task<Locality?> GetLocalityByIdAsync(int localityId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetLocalityById]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityID", localityId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapLocality(reader);
            }

            return null;
        }

        public async Task<List<Locality>> GetChildLocalitiesAsync(int parentLocalityId)
        {
            var localities = new List<Locality>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetChildLocalities]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@ParentLocalityID", parentLocalityId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                localities.Add(MapLocality(reader));
            }

            return localities;
        }

        public async Task<int> GetChildCountAsync(int parentLocalityId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetChildCount]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@ParentLocalityID", parentLocalityId);

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result ?? 0);
        }

        public async Task<int> CreateLocalityAsync(Locality locality)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspCreateLocality]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            // Add parameters for stored procedure
            command.Parameters.AddWithValue("@LocalityTitle", locality.LocalityTitle);
            command.Parameters.AddWithValue("@LocalityCode", locality.LocalityCode);
            command.Parameters.AddWithValue("@LocalityDescription", locality.LocalityDescription ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@LocalityTypeID", locality.LocalityTypeID);
            command.Parameters.AddWithValue("@ParentLocalityID", locality.ParentLocalityID ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@PracticeID", locality.PracticeID ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@PracticeLocationID", locality.PracticeLocationID ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SequenceNo", locality.SequenceNo ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Facilities", locality.Facilities ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsMultipleRoom", locality.IsMultipleRoom ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsHomeLeave", locality.IsHomeLeave ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@InsertedBy", locality.InsertedBy);
            command.Parameters.AddWithValue("@UserLoggingID", locality.UserLoggingID);

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result ?? 0);
        }

        public async Task<bool> UpdateLocalityAsync(Locality locality)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocality]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            // Add parameters for stored procedure
            command.Parameters.AddWithValue("@LocalityID", locality.LocalityID);
            command.Parameters.AddWithValue("@LocalityTitle", locality.LocalityTitle);
            command.Parameters.AddWithValue("@LocalityCode", locality.LocalityCode);
            command.Parameters.AddWithValue("@LocalityDescription", locality.LocalityDescription ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SequenceNo", locality.SequenceNo ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Facilities", locality.Facilities ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsMultipleRoom", locality.IsMultipleRoom ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsHomeLeave", locality.IsHomeLeave ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsActive", locality.IsActive);
            command.Parameters.AddWithValue("@UpdatedBy", locality.UpdatedBy);
            command.Parameters.AddWithValue("@UserLoggingID", locality.UserLoggingID);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> DeleteLocalityAsync(int localityId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspDeleteLocality]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityID", localityId);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> LocalityExistsAsync(int localityId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspLocalityExists]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityID", localityId);

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToBoolean(result ?? false);
        }

        public async Task<bool> LocalityCodeExistsAsync(string localityCode, int? excludeId = null)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspLocalityCodeExists]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityCode", localityCode);
            command.Parameters.AddWithValue("@ExcludeID", excludeId ?? (object)DBNull.Value);

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToBoolean(result ?? false);
        }

        public async Task<List<Locality>> GetAvailableRoomsAsync()
        {
            var localities = new List<Locality>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetAvailableRooms]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                localities.Add(MapLocality(reader));
            }

            return localities;
        }

        public async Task<List<LocalityType>> GetAllLocalityTypesAsync()
        {
            var localityTypes = new List<LocalityType>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetAllLocalityTypes]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                localityTypes.Add(MapLocalityType(reader));
            }

            return localityTypes;
        }

        public async Task<List<Locality>> SearchLocalitiesByNameAsync(string searchTerm, int? localityTypeId = null)
        {
            var localities = new List<Locality>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspSearchLocalitiesByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@SearchTerm", searchTerm);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId ?? (object)DBNull.Value);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                localities.Add(MapLocality(reader));
            }

            return localities;
        }

        public async Task<List<Locality>> SearchBuildingsByNameAsync(string searchTerm)
        {
            return await SearchLocalitiesByNameAsync(searchTerm, LocalityTypes.Building);
        }

        public async Task<List<Locality>> SearchWardsByNameAsync(string searchTerm)
        {
            return await SearchLocalitiesByNameAsync(searchTerm, LocalityTypes.Ward);
        }

        public async Task<List<Locality>> SearchRoomsByNameAsync(string searchTerm)
        {
            return await SearchLocalitiesByNameAsync(searchTerm, LocalityTypes.Room);
        }

        public async Task<Locality?> GetLocalityByNameAsync(string localityName, int localityTypeId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetLocalityByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityName", localityName);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapLocality(reader);
            }

            return null;
        }

        public async Task<bool> UpdateLocalityByNameAsync(string localityName, int localityTypeId, Locality locality)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocalityByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            // Add parameters for stored procedure
            command.Parameters.AddWithValue("@LocalityName", localityName);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);
            command.Parameters.AddWithValue("@NewLocalityTitle", locality.LocalityTitle);
            command.Parameters.AddWithValue("@NewLocalityCode", locality.LocalityCode);
            command.Parameters.AddWithValue("@LocalityDescription", locality.LocalityDescription ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SequenceNo", locality.SequenceNo ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Facilities", locality.Facilities ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsMultipleRoom", locality.IsMultipleRoom ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsHomeLeave", locality.IsHomeLeave ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsActive", locality.IsActive);
            command.Parameters.AddWithValue("@UpdatedBy", locality.UpdatedBy);
            command.Parameters.AddWithValue("@UserLoggingID", locality.UserLoggingID);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> DeleteLocalityByNameAsync(string localityName, int localityTypeId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspDeleteLocalityByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityName", localityName);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<Locality?> GetLocalityByCodeAsync(string localityCode, int localityTypeId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetLocalityByCode]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityCode", localityCode);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapLocality(reader);
            }

            return null;
        }

        public async Task<bool> UpdateLocalityByCodeAsync(string localityCode, int localityTypeId, Locality locality)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocalityByCode]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            // Add parameters for stored procedure
            command.Parameters.AddWithValue("@LocalityCode", localityCode);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);
            command.Parameters.AddWithValue("@NewLocalityTitle", locality.LocalityTitle);
            command.Parameters.AddWithValue("@NewLocalityCode", locality.LocalityCode);
            command.Parameters.AddWithValue("@LocalityDescription", locality.LocalityDescription ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SequenceNo", locality.SequenceNo ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Facilities", locality.Facilities ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsMultipleRoom", locality.IsMultipleRoom ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsHomeLeave", locality.IsHomeLeave ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@IsActive", locality.IsActive);
            command.Parameters.AddWithValue("@UpdatedBy", locality.UpdatedBy);
            command.Parameters.AddWithValue("@UserLoggingID", locality.UserLoggingID);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> UpdateRoomWardAsync(int roomId, int newWardId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateRoomWard]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@RoomID", roomId);
            command.Parameters.AddWithValue("@NewWardID", newWardId);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> UpdateWardBuildingAsync(int wardId, int newBuildingId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateWardBuilding]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@WardID", wardId);
            command.Parameters.AddWithValue("@NewBuildingID", newBuildingId);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> UpdateLocalityDescriptionAsync(int localityId, string description)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocalityDescription]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityID", localityId);
            command.Parameters.AddWithValue("@LocalityDescription", description ?? (object)DBNull.Value);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> UpdateLocalityDescriptionByNameAsync(string localityName, int localityTypeId, string description)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocalityDescriptionByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityName", localityName);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);
            command.Parameters.AddWithValue("@LocalityDescription", description ?? (object)DBNull.Value);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<bool> UpdateLocalityDescriptionByCodeAsync(string localityCode, int localityTypeId, string description)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdateLocalityDescriptionByCode]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@LocalityCode", localityCode);
            command.Parameters.AddWithValue("@LocalityTypeID", localityTypeId);
            command.Parameters.AddWithValue("@LocalityDescription", description ?? (object)DBNull.Value);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        private static Locality MapLocality(SqlDataReader reader)
        {
            return new Locality
            {
                LocalityID = reader.GetInt32("LocalityID"),
                LocalityTitle = reader.GetString("LocalityTitle"),
                LocalityCode = reader.GetString("LocalityCode"),
                LocalityDescription = reader.IsDBNull("LocalityDescription") ? null : reader.GetString("LocalityDescription"),
                LocalityTypeID = reader.GetInt32("LocalityTypeID"),
                ParentLocalityID = reader.IsDBNull("ParentLocalityID") ? null : reader.GetInt32("ParentLocalityID"),
                IsActive = reader.GetBoolean("IsActive"),
                IsDeleted = reader.GetBoolean("IsDeleted"),
                InsertedBy = reader.GetInt32("InsertedBy"),
                UpdatedBy = reader.GetInt32("UpdatedBy"),
                InsertedAt = reader.GetDateTime("InsertedAt"),
                UpdatedAt = reader.GetDateTime("UpdatedAt"),
                UserLoggingID = reader.GetInt64("UserLoggingID"),
                AnalysisTimestamp = (byte[])reader["AnalysisTimestamp"],
                PracticeID = reader.IsDBNull("PracticeID") ? null : reader.GetInt32("PracticeID"),
                PracticeLocationID = reader.IsDBNull("PracticeLocationID") ? null : reader.GetInt32("PracticeLocationID"),
                SequenceNo = reader.IsDBNull("SequenceNo") ? null : reader.GetInt32("SequenceNo"),
                IsOccupied = reader.GetBoolean("IsOccupied"),
                Facilities = reader.IsDBNull("Facilities") ? null : reader.GetString("Facilities"),
                IsMultipleRoom = reader.IsDBNull("IsMultipleRoom") ? null : reader.GetBoolean("IsMultipleRoom"),
                IsHomeLeave = reader.IsDBNull("IsHomeLeave") ? null : reader.GetBoolean("IsHomeLeave"),
                // Set ParentTitle if available in the result set
                ParentTitle = reader.HasColumn("ParentTitle") && !reader.IsDBNull("ParentTitle") 
                    ? reader.GetString("ParentTitle") : null
            };
        }

        private static LocalityType MapLocalityType(SqlDataReader reader)
        {
            return new LocalityType
            {
                LocalityTypeID = reader.GetInt32("LocalityTypeID"),
                LocalityTypeName = reader.IsDBNull("LocalityTypeName") ? null : reader.GetString("LocalityTypeName"),
                IsActive = reader.GetBoolean("IsActive"),
                IsDeleted = reader.GetBoolean("IsDeleted"),
                InsertedBy = reader.GetInt32("InsertedBy"),
                UpdatedBy = reader.GetInt32("UpdatedBy"),
                InsertedAt = reader.GetDateTime("InsertedAt"),
                UpdatedAt = reader.GetDateTime("UpdatedAt"),
                UserLoggingID = reader.GetInt64("UserLoggingID"),
                AnalysisTimestamp = (byte[])reader["AnalysisTimestamp"]
            };
        }
    }

    // Extension method to check if column exists in SqlDataReader
    public static class SqlDataReaderExtensions
    {
        public static bool HasColumn(this SqlDataReader reader, string columnName)
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }
    }
}
