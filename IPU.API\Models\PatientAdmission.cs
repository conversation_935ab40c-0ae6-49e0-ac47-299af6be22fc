namespace IPU.API.Models
{
    /// <summary>
    /// Represents a patient admission from [IPU].[tblPatientAdmission] table
    /// </summary>
    public class PatientAdmission
    {
        public int PatientAdmissionID { get; set; }
        public int? ReferralPatientID { get; set; }
        public int? PatientID { get; set; }
        public DateTime? AdmissionDate { get; set; }
        public bool IsPreviousAdmission { get; set; }
        public int? AdmissionRequestedID { get; set; }
        public DateTime? RequestedDate { get; set; }
        public int? AdmissionFromID { get; set; }
        public int? ReasonforAdmissionID { get; set; }
        public int? LocalityID { get; set; } // Room assignment
        public int? CPCTeamID { get; set; }
        public int? ProviderID { get; set; }
        public int PracticeID { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public int InsertedBy { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime InsertedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public long UserLoggingID { get; set; }
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
        public int? NoofGuest { get; set; }
        public string? Comments { get; set; }
        public int? AssignedNurseID { get; set; }
        public int? AssignedDoctorID { get; set; }
        public int? AdmissionStatusID { get; set; }
        public int? AdmissionSubStatusID { get; set; }
        public int? BedCategoryID { get; set; }
        public string? AdmissionType { get; set; }
        public int? FundingTypeID { get; set; }
        public string? PatientManagement { get; set; }
        public int? PatientLeaveID { get; set; }
        public int? SpecialityID { get; set; }
        public string? PatientCarer { get; set; }
        public string? TriageCode { get; set; }
        public string? SmokingStatus { get; set; }
        public string? FamilyViolence { get; set; }
        public string? AlcohalStatus { get; set; }
        public bool? Visitors { get; set; }
        public bool? ReligiousVisitors { get; set; }
        public bool? PrivacyStatementOne { get; set; }
        public string? PatientAdmissionFallReason { get; set; }
        public bool? PrivacyStatementTwo { get; set; }
        public DateTime? AdmissionTime { get; set; }
        public DateTime? HomeLeaveTime { get; set; }
        public DateTime? HomeLeaveDate { get; set; }
        public int? DischargeID { get; set; } // Links to discharge record
        public int? locationId { get; set; }
        public int? ACC45ID { get; set; }
        public string? Triage { get; set; }
        public string? FVOutcome { get; set; }
        public string? FamilyViolenceScreened { get; set; }
        public int? TriageID { get; set; }
        public DateTime? WaitingTime { get; set; }
        public DateTime? Timeseen { get; set; }
        public int? SeenByDoctorID { get; set; }
        public string? SeenByDoctorName { get; set; }
        public DateTime? Seendate { get; set; }
        public int? ReasonForNotScreeningID { get; set; }
        public bool? IsParked { get; set; } // Key field for medical discharge status
        public int? PrivacyStatment { get; set; }
        public string? Complaint { get; set; }
        public int? HCSReferralStatus { get; set; }
        public int? IPUArrivalMethodID { get; set; }

        // Navigation properties
        public Patient? Patient { get; set; }
        public Locality? Room { get; set; } // Room where patient is admitted
        public Discharge? Discharge { get; set; }
    }
}
