namespace IPU.API.Models
{
    /// <summary>
    /// Represents locality types from [IPU].[tblLocalityType] table
    /// 1 = Building, 2 = Ward, 3 = Room, 4 = Bed
    /// </summary>
    public class LocalityType
    {
        public int LocalityTypeID { get; set; }
        public string? LocalityTypeName { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public int InsertedBy { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime InsertedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public long UserLoggingID { get; set; }
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
    }

    /// <summary>
    /// Constants for locality types based on your data
    /// </summary>
    public static class LocalityTypes
    {
        public const int Building = 1;
        public const int Ward = 2;
        public const int Room = 3;
        public const int Bed = 4;
        
        public static readonly Dictionary<int, string> Names = new()
        {
            { Building, "Building" },
            { Ward, "Ward" },
            { Room, "Room" },
            { Bed, "Bed" }
        };
    }
}
