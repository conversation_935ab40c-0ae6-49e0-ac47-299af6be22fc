using System.ComponentModel.DataAnnotations;

namespace IPU.API.Models
{
    /// <summary>
    /// Model for IPU admission form data
    /// </summary>
    public class IPUAdmissionRequest
    {
        // Patient Identification (for MCP - either Name or NHI required)
        public string? PatientName { get; set; }
        public string? NHINumber { get; set; }
        
        // Core Admission Details
        [Required]
        public DateTime AdmissionDate { get; set; }
        
        [Required]
        public TimeSpan AdmissionTime { get; set; }
        
        [Required]
        public int ReasonID { get; set; }
        
        [Required]
        public string AssignedDoctorID { get; set; } = string.Empty;

        [Required]
        public int AdmissionSubStatusID { get; set; }

        [Required]
        public int SpecialityID { get; set; }

        [Required]
        public string AdmissionType { get; set; } = string.Empty; // "Acute" or "Arranged"

        [Required]
        public int LocationID { get; set; }

        [Required]
        public string PatientManagement { get; set; } = string.Empty; // "Inpatient", "Day Case", "Rest Home", "ED"
        
        [Required]
        public int RoomID { get; set; }
        
        public string? ACC45ID { get; set; }
        
        [Required]
        public int TriageID { get; set; }
        
        [Required]
        public string AlcoholInvolved { get; set; } = string.Empty;
        
        public DateTime? TreatmentStartedDate { get; set; }
        public TimeSpan? TreatmentStartedTime { get; set; }
        
        [Required]
        public int IPUArrivalMethodID { get; set; }
        
        public string? SeenByDoctorID { get; set; }
        
        public string? Comments { get; set; }
        public string? Complaint { get; set; }
        
        // Family Violence Screening
        [Required]
        public string FamilyViolenceScreened { get; set; } = string.Empty; // "Yes" or "No"

        [Required]
        public string FVOutcome { get; set; } = string.Empty; // "Positive", "Negative", "Historical"
        
        public int? ReasonForNotScreeningID { get; set; }
        
        // Visitor and Privacy Settings
        public bool VisitorFlag { get; set; }
        public bool ReligiousVisitors { get; set; }
        public bool PrivacyStatementOne { get; set; }
        public bool PrivacyStatementTwo { get; set; }
        
        // System fields
        public int PracticeID { get; set; } = 1;
        public int UserLoggingID { get; set; } = 1;
        public int InsertedBy { get; set; } = 1;
    }

    /// <summary>
    /// Response model for IPU admission creation
    /// </summary>
    public class IPUAdmissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? PatientAdmissionID { get; set; }
        public PatientInfo? Patient { get; set; }
    }

    /// <summary>
    /// Patient information for verification
    /// </summary>
    public class PatientInfo
    {
        public int PatientID { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string NHINumber { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Gender { get; set; } = string.Empty;
        public bool IsAdmitted { get; set; }
    }

    /// <summary>
    /// Patient search request for MCP
    /// </summary>
    public class PatientSearchRequest
    {
        public string? PatientName { get; set; }
        public string? NHINumber { get; set; }
    }

    /// <summary>
    /// Patient search response
    /// </summary>
    public class PatientSearchResponse
    {
        public bool Found { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<PatientInfo> Patients { get; set; } = new List<PatientInfo>();
    }

    // Dropdown Models
    public class DropdownItem
    {
        public int ID { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class IPUEnumeration
    {
        public int IPUEnumerationID { get; set; }
        public string Description { get; set; } = string.Empty;
        public int ParentID { get; set; }
    }

    public class Provider
    {
        public string ProviderUUID { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
    }

    public class AdmissionSubStatus
    {
        public int AdmissionSubStatusID { get; set; }
        public string AdmissionSubStatusName { get; set; } = string.Empty;
    }

    public class ReferralSpeciality
    {
        public int SpecialityID { get; set; }
        public string SpecialityName { get; set; } = string.Empty;
    }

    public class PracticeLocation
    {
        public int PracticeLocationID { get; set; }
        public string LocationName { get; set; } = string.Empty;
    }

    public class TriageScore
    {
        public int TriageScoreID { get; set; }
        public string TriageScoreName { get; set; } = string.Empty;
    }

    public class IpuArrivalMethod
    {
        public int IPUArrivalMethodID { get; set; }
        public string ArrivalMethod { get; set; } = string.Empty;
    }

    public class FamilyViolenceReason
    {
        public int ReasonForNotScreeningID { get; set; }
        public string ReasonForNotScreeningName { get; set; } = string.Empty;
    }

    public class ACC45
    {
        public string UUID { get; set; } = string.Empty;
        public string ACC45Number { get; set; } = string.Empty;
    }

    /// <summary>
    /// Comprehensive dropdown data for IPU admission form
    /// </summary>
    public class IPUAdmissionDropdownData
    {
        public List<IPUEnumeration> AdmissionReasons { get; set; } = new List<IPUEnumeration>();
        public List<Provider> Doctors { get; set; } = new List<Provider>();
        public List<AdmissionSubStatus> AdmissionSources { get; set; } = new List<AdmissionSubStatus>();
        public List<ReferralSpeciality> Specialities { get; set; } = new List<ReferralSpeciality>();
        public List<string> AdmissionTypes { get; set; } = new List<string> { "Acute", "Arranged" };
        public List<PracticeLocation> Locations { get; set; } = new List<PracticeLocation>();
        public List<string> PatientManagementTypes { get; set; } = new List<string> { "Inpatient", "Day Case", "Rest Home", "ED" };
        public List<DropdownItem> AvailableRooms { get; set; } = new List<DropdownItem>();
        public List<TriageScore> TriageScores { get; set; } = new List<TriageScore>();
        public List<string> AlcoholInvolvedOptions { get; set; } = new List<string> 
        { 
            "No Alcohol Involved", 
            "Unknown", 
            "Yes Alcohol Involved", 
            "Secondary (Others consumed alcohol)" 
        };
        public List<IpuArrivalMethod> ArrivalMethods { get; set; } = new List<IpuArrivalMethod>();
        public List<string> FamilyViolenceScreenedOptions { get; set; } = new List<string> { "Yes", "No" };
        public List<string> FVOutcomeOptions { get; set; } = new List<string> { "Positive", "Negative", "Historical" };
        public List<FamilyViolenceReason> FamilyViolenceReasons { get; set; } = new List<FamilyViolenceReason>();
        public List<ACC45> ACC45Numbers { get; set; } = new List<ACC45>();
    }
}
