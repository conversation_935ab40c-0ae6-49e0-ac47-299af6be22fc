using IPU.API.Data;
using IPU.API.Models;
using Microsoft.AspNetCore.Mvc;

namespace IPU.API.Controllers
{
    /// <summary>
    /// Controller for IPU admission operations designed for MCP server integration
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class IPUAdmissionController : ControllerBase
    {
        private readonly IIPUAdmissionRepository _admissionRepository;
        private readonly ILogger<IPUAdmissionController> _logger;

        public IPUAdmissionController(
            IIPUAdmissionRepository admissionRepository,
            ILogger<IPUAdmissionController> logger)
        {
            _admissionRepository = admissionRepository;
            _logger = logger;
        }

        /// <summary>
        /// Search for patients by name or NHI number (for MCP verification)
        /// </summary>
        /// <param name="request">Search criteria containing patient name or NHI</param>
        /// <returns>List of matching patients</returns>
        /// <response code="200">Returns the list of matching patients</response>
        /// <response code="400">If search criteria is invalid</response>
        [HttpPost("search-patients")]
        [ProducesResponseType(typeof(PatientSearchResponse), 200)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<PatientSearchResponse>> SearchPatients([FromBody] PatientSearchRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.PatientName) && string.IsNullOrWhiteSpace(request.NHINumber))
                {
                    return BadRequest(new PatientSearchResponse 
                    { 
                        Message = "Please provide either patient name or NHI number for search." 
                    });
                }

                var result = await _admissionRepository.SearchPatientsAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching for patients");
                return StatusCode(500, new PatientSearchResponse 
                { 
                    Message = "An error occurred while searching for patients." 
                });
            }
        }

        /// <summary>
        /// Get patient by name (for MCP - exact match)
        /// </summary>
        /// <param name="patientName">Patient full name</param>
        /// <returns>Patient information if found</returns>
        /// <response code="200">Returns patient information</response>
        /// <response code="404">If patient not found</response>
        [HttpGet("patient/by-name/{patientName}")]
        [ProducesResponseType(typeof(PatientInfo), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<PatientInfo>> GetPatientByName(string patientName)
        {
            try
            {
                var patient = await _admissionRepository.GetPatientByNameAsync(patientName);
                
                if (patient == null)
                {
                    return NotFound(new { message = $"Patient '{patientName}' not found." });
                }

                return Ok(patient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting patient by name: {PatientName}", patientName);
                return StatusCode(500, new { message = "An error occurred while retrieving patient information." });
            }
        }

        /// <summary>
        /// Get patient by NHI number (for MCP)
        /// </summary>
        /// <param name="nhiNumber">NHI number</param>
        /// <returns>Patient information if found</returns>
        /// <response code="200">Returns patient information</response>
        /// <response code="404">If patient not found</response>
        [HttpGet("patient/by-nhi/{nhiNumber}")]
        [ProducesResponseType(typeof(PatientInfo), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<PatientInfo>> GetPatientByNHI(string nhiNumber)
        {
            try
            {
                var patient = await _admissionRepository.GetPatientByNHIAsync(nhiNumber);
                
                if (patient == null)
                {
                    return NotFound(new { message = $"Patient with NHI '{nhiNumber}' not found." });
                }

                return Ok(patient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting patient by NHI: {NHINumber}", nhiNumber);
                return StatusCode(500, new { message = "An error occurred while retrieving patient information." });
            }
        }

        /// <summary>
        /// Create a new IPU admission (designed for MCP natural language processing)
        /// Supports admission by patient name or NHI number
        /// </summary>
        /// <param name="request">Admission details</param>
        /// <returns>Admission result</returns>
        /// <response code="200">Admission created successfully</response>
        /// <response code="400">If admission data is invalid</response>
        /// <response code="404">If patient not found</response>
        /// <response code="409">If patient is already admitted</response>
        [HttpPost("admit-patient")]
        [ProducesResponseType(typeof(IPUAdmissionResponse), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(409)]
        public async Task<ActionResult<IPUAdmissionResponse>> AdmitPatient([FromBody] IPUAdmissionRequest request)
        {
            try
            {
                // Validate that either patient name or NHI is provided
                if (string.IsNullOrWhiteSpace(request.PatientName) && string.IsNullOrWhiteSpace(request.NHINumber))
                {
                    return BadRequest(new IPUAdmissionResponse 
                    { 
                        Message = "Please provide either patient name or NHI number for admission." 
                    });
                }

                var result = await _admissionRepository.CreateAdmissionAsync(request);

                if (!result.Success)
                {
                    if (result.Message.Contains("not found"))
                    {
                        return NotFound(result);
                    }
                    else if (result.Message.Contains("already admitted"))
                    {
                        return Conflict(result);
                    }
                    else
                    {
                        return BadRequest(result);
                    }
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating IPU admission");
                return StatusCode(500, new IPUAdmissionResponse 
                { 
                    Message = "An error occurred while creating the admission." 
                });
            }
        }

        /// <summary>
        /// Get all dropdown data needed for IPU admission form
        /// </summary>
        /// <returns>Comprehensive dropdown data for the admission form</returns>
        /// <response code="200">Returns all dropdown data</response>
        [HttpGet("dropdown-data")]
        [ProducesResponseType(typeof(IPUAdmissionDropdownData), 200)]
        public async Task<ActionResult<IPUAdmissionDropdownData>> GetDropdownData()
        {
            try
            {
                var data = await _admissionRepository.GetAdmissionDropdownDataAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dropdown data");
                return StatusCode(500, new { message = "An error occurred while retrieving dropdown data." });
            }
        }

        /// <summary>
        /// Get admission reasons
        /// </summary>
        /// <returns>List of admission reasons</returns>
        [HttpGet("dropdown/admission-reasons")]
        [ProducesResponseType(typeof(List<IPUEnumeration>), 200)]
        public async Task<ActionResult<List<IPUEnumeration>>> GetAdmissionReasons()
        {
            try
            {
                var reasons = await _admissionRepository.GetAdmissionReasonsAsync();
                return Ok(reasons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admission reasons");
                return StatusCode(500, new { message = "An error occurred while retrieving admission reasons." });
            }
        }

        /// <summary>
        /// Get available doctors
        /// </summary>
        /// <returns>List of doctors</returns>
        [HttpGet("dropdown/doctors")]
        [ProducesResponseType(typeof(List<Provider>), 200)]
        public async Task<ActionResult<List<Provider>>> GetDoctors()
        {
            try
            {
                var doctors = await _admissionRepository.GetDoctorsAsync();
                return Ok(doctors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting doctors");
                return StatusCode(500, new { message = "An error occurred while retrieving doctors." });
            }
        }

        /// <summary>
        /// Get admission sources
        /// </summary>
        /// <returns>List of admission sources</returns>
        [HttpGet("dropdown/admission-sources")]
        [ProducesResponseType(typeof(List<AdmissionSubStatus>), 200)]
        public async Task<ActionResult<List<AdmissionSubStatus>>> GetAdmissionSources()
        {
            try
            {
                var sources = await _admissionRepository.GetAdmissionSourcesAsync();
                return Ok(sources);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admission sources");
                return StatusCode(500, new { message = "An error occurred while retrieving admission sources." });
            }
        }

        /// <summary>
        /// Get specialities
        /// </summary>
        /// <returns>List of specialities</returns>
        [HttpGet("dropdown/specialities")]
        [ProducesResponseType(typeof(List<ReferralSpeciality>), 200)]
        public async Task<ActionResult<List<ReferralSpeciality>>> GetSpecialities()
        {
            try
            {
                var specialities = await _admissionRepository.GetSpecialitiesAsync();
                return Ok(specialities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting specialities");
                return StatusCode(500, new { message = "An error occurred while retrieving specialities." });
            }
        }

        /// <summary>
        /// Get practice locations
        /// </summary>
        /// <returns>List of locations</returns>
        [HttpGet("dropdown/locations")]
        [ProducesResponseType(typeof(List<PracticeLocation>), 200)]
        public async Task<ActionResult<List<PracticeLocation>>> GetLocations()
        {
            try
            {
                var locations = await _admissionRepository.GetLocationsAsync();
                return Ok(locations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting locations");
                return StatusCode(500, new { message = "An error occurred while retrieving locations." });
            }
        }

        /// <summary>
        /// Get available rooms for a specific location
        /// </summary>
        /// <param name="locationId">Location ID (optional)</param>
        /// <returns>List of available rooms</returns>
        [HttpGet("dropdown/rooms")]
        [ProducesResponseType(typeof(List<DropdownItem>), 200)]
        public async Task<ActionResult<List<DropdownItem>>> GetAvailableRooms([FromQuery] int? locationId = null)
        {
            try
            {
                var rooms = await _admissionRepository.GetAvailableRoomsAsync(locationId);
                return Ok(rooms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available rooms");
                return StatusCode(500, new { message = "An error occurred while retrieving available rooms." });
            }
        }

        /// <summary>
        /// Get triage scores
        /// </summary>
        /// <returns>List of triage scores</returns>
        [HttpGet("dropdown/triage-scores")]
        [ProducesResponseType(typeof(List<TriageScore>), 200)]
        public async Task<ActionResult<List<TriageScore>>> GetTriageScores()
        {
            try
            {
                var scores = await _admissionRepository.GetTriageScoresAsync();
                return Ok(scores);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting triage scores");
                return StatusCode(500, new { message = "An error occurred while retrieving triage scores." });
            }
        }

        /// <summary>
        /// Get IPU arrival methods
        /// </summary>
        /// <returns>List of arrival methods</returns>
        [HttpGet("dropdown/arrival-methods")]
        [ProducesResponseType(typeof(List<IpuArrivalMethod>), 200)]
        public async Task<ActionResult<List<IpuArrivalMethod>>> GetArrivalMethods()
        {
            try
            {
                var methods = await _admissionRepository.GetArrivalMethodsAsync();
                return Ok(methods);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting arrival methods");
                return StatusCode(500, new { message = "An error occurred while retrieving arrival methods." });
            }
        }

        /// <summary>
        /// Get family violence screening reasons
        /// </summary>
        /// <returns>List of family violence reasons</returns>
        [HttpGet("dropdown/family-violence-reasons")]
        [ProducesResponseType(typeof(List<FamilyViolenceReason>), 200)]
        public async Task<ActionResult<List<FamilyViolenceReason>>> GetFamilyViolenceReasons()
        {
            try
            {
                var reasons = await _admissionRepository.GetFamilyViolenceReasonsAsync();
                return Ok(reasons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family violence reasons");
                return StatusCode(500, new { message = "An error occurred while retrieving family violence reasons." });
            }
        }

        /// <summary>
        /// Get ACC45 numbers for a specific patient
        /// </summary>
        /// <param name="patientId">Patient ID</param>
        /// <returns>List of ACC45 numbers</returns>
        [HttpGet("dropdown/acc45-numbers/{patientId}")]
        [ProducesResponseType(typeof(List<ACC45>), 200)]
        public async Task<ActionResult<List<ACC45>>> GetACC45Numbers(int patientId)
        {
            try
            {
                var acc45Numbers = await _admissionRepository.GetACC45NumbersAsync(patientId);
                return Ok(acc45Numbers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ACC45 numbers for patient {PatientId}", patientId);
                return StatusCode(500, new { message = "An error occurred while retrieving ACC45 numbers." });
            }
        }

        /// <summary>
        /// MCP-friendly endpoint: Admit patient by natural language query
        /// Supports queries like "admit patient John Smith" or "admit patient with NHI ABC1234"
        /// </summary>
        /// <param name="query">Natural language query containing patient identifier</param>
        /// <param name="admissionData">Admission form data</param>
        /// <returns>Admission result</returns>
        [HttpPost("admit-by-query")]
        [ProducesResponseType(typeof(IPUAdmissionResponse), 200)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<IPUAdmissionResponse>> AdmitPatientByQuery(
            [FromQuery] string query,
            [FromBody] IPUAdmissionRequest admissionData)
        {
            try
            {
                // Parse the query to extract patient identifier
                var patientIdentifier = ExtractPatientIdentifier(query);

                if (string.IsNullOrWhiteSpace(patientIdentifier))
                {
                    return BadRequest(new IPUAdmissionResponse
                    {
                        Message = "Could not extract patient identifier from query. Please specify patient name or NHI number."
                    });
                }

                // Determine if it's a name or NHI
                if (IsNHIFormat(patientIdentifier))
                {
                    admissionData.NHINumber = patientIdentifier;
                    admissionData.PatientName = null;
                }
                else
                {
                    admissionData.PatientName = patientIdentifier;
                    admissionData.NHINumber = null;
                }

                return await AdmitPatient(admissionData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing admission query: {Query}", query);
                return StatusCode(500, new IPUAdmissionResponse
                {
                    Message = "An error occurred while processing the admission query."
                });
            }
        }

        /// <summary>
        /// Extract patient identifier from natural language query
        /// </summary>
        private string ExtractPatientIdentifier(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                return string.Empty;

            query = query.ToLower().Trim();

            // Look for NHI pattern
            var nhiMatch = System.Text.RegularExpressions.Regex.Match(query, @"\b[a-z]{3}\d{4}\b", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (nhiMatch.Success)
            {
                return nhiMatch.Value.ToUpper();
            }

            // Look for patient name patterns
            var namePatterns = new[]
            {
                @"admit\s+patient\s+(.+?)(?:\s+with|$)",
                @"admit\s+(.+?)(?:\s+to|$)",
                @"patient\s+(.+?)(?:\s+admission|$)"
            };

            foreach (var pattern in namePatterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(query, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (match.Success && match.Groups.Count > 1)
                {
                    var name = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(name) && !name.Contains("nhi"))
                    {
                        return name;
                    }
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// Check if the identifier matches NHI format (3 letters + 4 digits)
        /// </summary>
        private bool IsNHIFormat(string identifier)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(identifier, @"^[A-Z]{3}\d{4}$", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }
    }
}
