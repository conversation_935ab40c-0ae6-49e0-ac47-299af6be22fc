namespace IPU.API.Models
{
    /// <summary>
    /// Represents a patient from [Profile].[tblPatient] table
    /// </summary>
    public class Patient
    {
        public int PatientID { get; set; }
        public int? GPID { get; set; }
        public string? ChartNumber { get; set; }
        public string? Extension { get; set; }
        public string? PracticeRemarks { get; set; }
        public bool IsInsured { get; set; }
        public string? InsuredRemarks { get; set; }
        public int? SmokingTypeID { get; set; }
        public byte AccountHolderTypeID { get; set; }
        public int? AccountHolderProfileID { get; set; }
        public int? AccoundHolderEmployerID { get; set; }
        public bool IsComunityServiceCard { get; set; }
        public string? ComunityServiceCardNo { get; set; }
        public DateTime? ComunityServiceCardExipryDate { get; set; }
        public bool ComunityServiceCardSighted { get; set; }
        public bool IsHealthCard { get; set; }
        public string? HealthCardNo { get; set; }
        public DateTime? HealthCardExpiryDate { get; set; }
        public bool HealthCardSighted { get; set; }
        public string? WINZ { get; set; }
        public int? IsTransferOfRecords { get; set; }
        public string? TransferofRecordsRemarks { get; set; }
        public int? EnrollmentTypeID { get; set; }
        public int? EnrollmentStatusID { get; set; }
        public DateTime? EnrollmentDate { get; set; }
        public int? EnrollmentMethodID { get; set; }
        public int? EnrollmentVerifierID { get; set; }
        public int? FundingStatusID { get; set; }
        public string? RejectionReason { get; set; }
        public DateTime? FundingFrom { get; set; }
        public DateTime? FundingTo { get; set; }
        public int? AccountGroupID { get; set; }
        public int? GMSCodeID { get; set; }
        public int? ResidentialStatusID { get; set; }
        public int? RegisterStatusID { get; set; }
        public bool? IsNKA { get; set; }
        public bool? IsNKDA { get; set; }
        public bool IsHighCare { get; set; }
        public string? HighCareReason { get; set; }
        public bool IsCarePlan { get; set; }
        public bool IsNewRegisteration { get; set; }
        public bool? IsOptOff { get; set; }
        public bool IsOptOffACK { get; set; }
        public bool IsBPGraph { get; set; }
        public bool IsBMIGraph { get; set; }
        public bool IsINRGraph { get; set; }
        public bool IsHbA1cGraph { get; set; }
        public int ASRMarkStatusID { get; set; }
        public decimal? Balance { get; set; }
        public bool IsHeightGraph { get; set; }
        public bool IsWeightGraph { get; set; }
        public bool IsHcGraph { get; set; }
        public int? PracticeID { get; set; }
        public byte? IsConsenttoShare { get; set; }
        public decimal? MedTechBalance { get; set; }
        public DateTime? MedTechBalanceDate { get; set; }
        public DateTime? MedTechDATELASTPAY { get; set; }
        public DateTime? MedTechDATELASTSTMT { get; set; }
        public byte NESStatusID { get; set; }
        public string? NESComments { get; set; }
        public bool IsHeartRate { get; set; }
        public long? UserLoggingID { get; set; }
        public bool IsPremature { get; set; }
        public byte? PrematureWeek { get; set; }
        public int? PatientPHOID { get; set; }
        public DateTime? ConsultUpdatedAt { get; set; }
        public int? OccupationID { get; set; }
        public int? NIRSearchID { get; set; }
        public int? MasterPatientID { get; set; }
        public int? LastASRCodeStatus { get; set; }
        public DateTime? LastASRCodeDate { get; set; }
        public bool IsGP2GP { get; set; }
        public bool? IsVideoConsult { get; set; }
        public DateTime? EnrollmentEndDate { get; set; }
        public bool IsNIRQueryStatus { get; set; }
        public bool IsNIRQueryStatusACK { get; set; }
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
        public string? ASRErrorCode { get; set; }
        public string? ASRErrorText { get; set; }
        public DateTime? ColdStoreDate { get; set; }
        public int? ColdStoreDateTypeID { get; set; }
        public string? EnrollmentID { get; set; }
        public DateTime? EnrollmentExpiryDate { get; set; }
        public byte? DataSourceID { get; set; }
        public bool? IsComunityServiceCardByJob { get; set; }
        public bool? IshealthCardByJob { get; set; }
        public bool? IsDummey { get; set; }
        public int? InsertedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? InsertedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? EnrollmentNotes { get; set; }
        public string? MedicareNo { get; set; }
        public string? IRN { get; set; }
        public DateTime? MedicareIRNExpiry { get; set; }
        public string? DVANo { get; set; }
        public DateTime? DVADate { get; set; }
        public string? PensionHCCNo { get; set; }
        public DateTime? PensionHCCExpiry { get; set; }
        public int? PensionCardTypeID { get; set; }
        public string? SafetyNetNo { get; set; }
        public int? UsualAccountID { get; set; }
        public bool IsRegCTG { get; set; }
        public bool IsMyAgePortal { get; set; }
        public int? DVAConditionID { get; set; }
        public string? MDSClientKey { get; set; }
        public string? OldRefNo { get; set; }
        public string? NDISNumber { get; set; }
        public int? HealthFundID { get; set; }
        public DateTime? LastCheckedCSCDate { get; set; }
        public DateTime? LastCheckedHUHCDate { get; set; }
        public int? NIRAgeFrom { get; set; }
        public int? NIRAgeTO { get; set; }
        public int? MedtechACHolderID { get; set; }
        public int? MedtechACHolderTypeID { get; set; }
        public string? ErrorDescription { get; set; }
        public string? PresentingComplaints { get; set; }
        public string? Notes { get; set; }
        public DateTime? CommunityServiceEffectiveDate { get; set; }
        public string? CSCEnrolmentID { get; set; }
        public bool IsPregnant { get; set; }
        public int? CarePlanCoordinatorID { get; set; }
        public bool IsAudiogram { get; set; }
        public int? BreastScreenID { get; set; }
        public bool IsConsentForExperienceSurvey { get; set; }
        public DateTime? IQIFundingInclusionDate { get; set; }
        public bool? IsCareplus { get; set; }
        public DateTime? CarePlusStartDate { get; set; }
        public DateTime? CarePlusEnddate { get; set; }
        public int? CarePlusLincStatusID { get; set; }
        public bool IsH1OptOut { get; set; }
        public DateTime? H1OptOutChangeDate { get; set; }
        public bool UnSubscribePortalGreeting { get; set; }
        public bool IsArchived { get; set; }
        public bool? IsDeletedHighCare { get; set; }
        public int? PharmacyID { get; set; }
        public string? OtherPharmacy { get; set; }
        public byte? IsConsentToImportClinicalRecords { get; set; }
        public byte? IsConsentToShareMyRecordOnSEHR { get; set; }
        public byte? IsConsentToShareClinicalRecords { get; set; }
        public byte? IsConsentToShareHealth1 { get; set; }
        public byte? IsConsentToPrivateEmailAddress { get; set; }
        public bool IsFamilyPlanningBefore { get; set; }
        public int? FamilyPlanningClinicID { get; set; }
        public bool IsContactGP { get; set; }
        public int? PharmacyLocationID { get; set; }
        public bool? IsHUReportReviewed { get; set; }
        public int? HUReportReviewedBy { get; set; }
        public DateTime? HUReportReviewedDate { get; set; }
        public int? ResidentialStatusProofID { get; set; }
        public byte? IsConsentToShowOptOff { get; set; }
        public string? BPACAddress { get; set; }
        public DateTime? LastMenstrualPeriod { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public string? pNHINumber { get; set; }
        public string? pFirstName { get; set; }
        public string? pMiddleName { get; set; }
        public string? pFamilyName { get; set; }
        public string? pFullName { get; set; }
        public string? pPreferredName { get; set; }
        public int? pGenderID { get; set; }
        public DateTime? pDOB { get; set; }
        public string? pCellNumber { get; set; }
        public string? pDayPhone { get; set; }
        public string? pEmail { get; set; }
        public bool pIsDeleted { get; set; }
        public bool pIsActive { get; set; }
        public bool IsAdmitted { get; set; } // Key field for admission status
        public int CovidVaccinationStatus { get; set; }
        public int? ReferrerID { get; set; }
        public string? ReferrerOther { get; set; }
        public string? SendingPracticeNameEDI { get; set; }
        public DateTime? H1SouthAddressChangeDate { get; set; }
        public byte? ConsentToShareEncNotesWithH1 { get; set; }
        public int? H1OPTStatus { get; set; }
        public byte? IsConsentToShareNZIPS { get; set; }
        public Guid HealthonePatientUUID { get; set; }
        public Guid HealthoneNotesUUID { get; set; }
        public byte? IsNZCitizen { get; set; }
        public bool AllowRepeatRxWithoutPayment { get; set; }
        public byte? LastReferralStatusID { get; set; }
        public byte? GulfIslandID { get; set; }
        public bool IsInterpreter { get; set; }
        public bool IsFamilySupport { get; set; }
        public int? PreferredInterpreterGenderID { get; set; }
        public int? PreferredInterPreterLanguageID { get; set; }
        public int? PlaceofDeathID { get; set; }
        public int? ExternalProviderAddressBookID { get; set; }
        public int? LivingStatusID { get; set; }
        public int? EpisodeEndSupportID { get; set; }
        public byte? ComunityServiceCardStatusID { get; set; }
        public byte? HealthCardStatusID { get; set; }
        public bool? LivesInGulfIsland { get; set; }
        public byte? IsConsentToShareDiagnoseCodewithTwo { get; set; }

        // Navigation properties
        public Profile? Profile { get; set; }
        public List<PatientAdmission> PatientAdmissions { get; set; } = new List<PatientAdmission>();
    }
}
