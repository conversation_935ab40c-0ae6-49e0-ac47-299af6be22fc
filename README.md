# IPU Configuration API

A comprehensive .NET Core Web API for complete management of IPU (Inpatient Unit) Buildings, Wards, and Rooms using a unified database architecture with advanced search, update, and relationship management capabilities.

## Overview

This API provides **complete CRUD operations** with multiple access methods for managing healthcare facility localities. It uses a single table `[IPU].[tblLocality]` to store all locality types (Buildings, Wards, Rooms, and Beds), differentiated by `LocalityTypeID`:

- **LocalityTypeID = 1**: Building
- **LocalityTypeID = 2**: Ward
- **LocalityTypeID = 3**: Room
- **LocalityTypeID = 4**: Bed

## Key Features

🏗️ **Complete CRUD Operations** - Create, Read, Update, Delete by ID, Name, or Code
🔍 **Advanced Search** - Search localities by name with intelligent ranking
🔄 **Flexible Updates** - Update by ID, Name, or Code with validation
🏢 **Relationship Management** - Move rooms between wards, wards between buildings
🚀 **Simple Creation** - Minimal input required (just name and parent)
📊 **Hierarchical Data** - Automatic child count calculation
🛡️ **Data Integrity** - Soft deletes, validation, and referential integrity
⚡ **Auto-Code Generation** - Smart, unique code generation
🌐 **RESTful Design** - Clean, intuitive API endpoints
📚 **Comprehensive Documentation** - Interactive Swagger UI

## Database Schema

### Main Table: `[IPU].[tblLocality]`
- Contains all locality records (Buildings, Wards, Rooms, Beds)
- Uses hierarchical structure with `ParentLocalityID`
- Differentiated by `LocalityTypeID` foreign key

### Lookup Table: `[IPU].[tblLocalityType]`
- Contains locality type definitions
- Referenced by `LocalityTypeID` in main table

## API Endpoints

### Buildings (LocalityTypeID = 1)

- **GET** `/api/configuration/buildings` - Get all buildings with ward count
- **GET** `/api/configuration/buildings/{id}` - Get specific building
- **POST** `/api/configuration/buildings` - Create new building
- **PUT** `/api/configuration/buildings/{id}` - Update building
- **DELETE** `/api/configuration/buildings/{id}` - Delete building

### Wards (LocalityTypeID = 2)

- **GET** `/api/configuration/wards` - Get all wards with room count
- **GET** `/api/configuration/buildings/{buildingId}/wards` - Get wards by building

### Rooms (LocalityTypeID = 3)

- **GET** `/api/configuration/rooms` - Get all rooms
- **GET** `/api/configuration/rooms/available` - Get available (unoccupied) rooms
- **GET** `/api/configuration/wards/{wardId}/rooms` - Get rooms by ward

### Search Operations

- **GET** `/api/configuration/buildings/search?searchTerm={term}` - Search buildings by name
- **GET** `/api/configuration/wards/search?searchTerm={term}` - Search wards by name
- **GET** `/api/configuration/rooms/search?searchTerm={term}` - Search rooms by name
- **GET** `/api/configuration/search?searchTerm={term}&localityTypeId={typeId}` - Search all localities by name (optional type filter)

### Update by Name Operations

- **PUT** `/api/configuration/buildings/update-by-name/{buildingName}` - Update building by name
- **PUT** `/api/configuration/wards/update-by-name/{wardName}` - Update ward by name
- **PUT** `/api/configuration/rooms/update-by-name/{roomName}` - Update room by name

### Update by Code Operations

- **PUT** `/api/configuration/buildings/update-by-code/{buildingCode}` - Update building by code
- **PUT** `/api/configuration/wards/update-by-code/{wardCode}` - Update ward by code
- **PUT** `/api/configuration/rooms/update-by-code/{roomCode}` - Update room by code

### Parent Relationship Updates

- **PUT** `/api/configuration/rooms/{roomId}/move-to-ward` - Move room to different ward
- **PUT** `/api/configuration/wards/{wardId}/move-to-building` - Move ward to different building

### Delete by Name Operations

- **DELETE** `/api/configuration/wards/delete-by-name/{wardName}` - Delete ward by name
- **DELETE** `/api/configuration/rooms/delete-by-name/{roomName}` - Delete room by name

### Simple Create Operations (Minimal Input)

- **POST** `/api/configuration/buildings/simple` - Create building with just name and description
- **POST** `/api/configuration/wards/simple` - Create ward with name, parent building name, and description
- **POST** `/api/configuration/rooms/simple` - Create room with name, parent ward name, and description

### Utility Endpoints

- **GET** `/health` - Health check
- **GET** `/test` - API test endpoint

## Configuration

### Connection String
Update `appsettings.json` with your database connection:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=dbserver-local;Database=PMS_NZ_LOCAL_NZTFS;Integrated Security=true;Encrypt=false;TrustServerCertificate=false;Connection Timeout=60"
  }
}
```

### Required Stored Procedures
Execute the SQL scripts in `StoredProcedures/Required-Stored-Procedures-New.sql` to create the required stored procedures.

## Running the API

1. **Build the project:**
   ```bash
   dotnet build
   ```

2. **Run the API:**
   ```bash
   dotnet run
   ```

3. **Access Swagger UI:**
   - Development: `http://localhost:5000/swagger`
   - Or check the console output for the actual URL

## API Response Format

All endpoints return responses in this format:

```json
{
  "success": true,
  "message": "Success message",
  "data": { /* actual data */ },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Example Usage

### Get All Buildings
```http
GET /api/configuration/buildings
```

Response:
```json
{
  "success": true,
  "message": "Buildings retrieved successfully",
  "data": [
    {
      "localityID": 1,
      "localityTitle": "Main Building",
      "localityCode": "MB001",
      "localityDescription": "Main hospital building",
      "localityTypeID": 1,
      "localityTypeName": "Building",
      "parentLocalityID": null,
      "isActive": true,
      "childCount": 5
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Create New Building
```http
POST /api/configuration/buildings
Content-Type: application/json

{
  "localityTitle": "New Building",
  "localityCode": "NB001",
  "localityDescription": "New hospital building",
  "localityTypeID": 1,
  "facilities": "ICU, Emergency"
}
```

### Search Buildings by Name
```http
GET /api/configuration/buildings/search?searchTerm=Main
```

Response:
```json
{
  "success": true,
  "message": "Found 2 buildings matching 'Main'",
  "data": [
    {
      "localityID": 1,
      "localityTitle": "Main Building",
      "localityCode": "MB001",
      "localityDescription": "Main hospital building",
      "localityTypeID": 1,
      "localityTypeName": "Building",
      "parentLocalityID": null,
      "isActive": true,
      "childCount": 5
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Update Building by Name
```http
PUT /api/configuration/buildings/update-by-name/Main Building
Content-Type: application/json

{
  "localityTitle": "Main Hospital Building",
  "localityCode": "MHB001",
  "localityDescription": "Updated main hospital building",
  "facilities": "ICU, Emergency, Surgery",
  "isActive": true
}
```

Response:
```json
{
  "success": true,
  "message": "Building 'Main Building' updated successfully",
  "data": {
    "localityID": 1,
    "localityTitle": "Main Hospital Building",
    "localityCode": "MHB001",
    "localityDescription": "Updated main hospital building",
    "localityTypeID": 1,
    "localityTypeName": "Building",
    "facilities": "ICU, Emergency, Surgery",
    "isActive": true,
    "childCount": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Simple Create Building
```http
POST /api/configuration/buildings/simple
Content-Type: application/json

{
  "localityTitle": "Emergency Building",
  "localityDescription": "New emergency department building"
}
```

### Simple Create Ward
```http
POST /api/configuration/wards/simple
Content-Type: application/json

{
  "localityTitle": "ICU Ward",
  "parentLocalityName": "Main Building",
  "localityDescription": "Intensive care unit ward"
}
```

### Simple Create Room
```http
POST /api/configuration/rooms/simple
Content-Type: application/json

{
  "localityTitle": "Room 101",
  "parentLocalityName": "ICU Ward",
  "localityDescription": "Private patient room"
}
```

### Delete Ward by Name
```http
DELETE /api/configuration/wards/delete-by-name/ICU Ward
```

### Delete Room by Name
```http
DELETE /api/configuration/rooms/delete-by-name/Room 101
```

### Update Building by Code
```http
PUT /api/configuration/buildings/update-by-code/BLDEM
Content-Type: application/json

{
  "localityTitle": "Emergency Department Building",
  "localityCode": "BLDEM01",
  "localityDescription": "Updated emergency building",
  "facilities": "Emergency, Trauma, Surgery",
  "isActive": true
}
```

### Move Room to Different Ward
```http
PUT /api/configuration/rooms/123/move-to-ward
Content-Type: application/json

{
  "newWardName": "ICU Ward"
}
```

### Move Ward to Different Building
```http
PUT /api/configuration/wards/456/move-to-building
Content-Type: application/json

{
  "newBuildingName": "Main Building"
}
```

## Features

- **Unified Architecture**: Single table for all locality types
- **Hierarchical Structure**: Buildings → Wards → Rooms → Beds
- **Count Information**: Automatic child count calculation
- **Soft Delete**: Records are marked as deleted, not physically removed
- **Validation**: Code uniqueness and parent-child relationship validation
- **Swagger Documentation**: Interactive API documentation
- **CORS Enabled**: Cross-origin requests supported
- **Logging**: Comprehensive logging for debugging

## Technology Stack

- **.NET 9.0** - Web API framework
- **Microsoft.Data.SqlClient** - Database connectivity
- **Swashbuckle.AspNetCore** - API documentation
- **SQL Server** - Database

## Project Structure

```
IPU.API/
├── Controllers/
│   └── ConfigurationController.cs    # Main API controller
├── Data/
│   ├── ILocalityRepository.cs        # Repository interface
│   └── LocalityRepository.cs         # Repository implementation
├── DTOs/
│   └── LocalityDto.cs               # Data transfer objects
├── Models/
│   ├── Locality.cs                  # Main entity model
│   └── LocalityType.cs              # Lookup entity model
├── Program.cs                       # Application startup
└── appsettings.json                 # Configuration
```

## Notes

- The API uses stored procedures for all database operations
- All stored procedures need to be created manually in the database
- The API expects the database to already exist with the required tables
- User authentication/authorization is not implemented (add as needed)
- The API uses hardcoded user IDs (1) for InsertedBy/UpdatedBy fields
