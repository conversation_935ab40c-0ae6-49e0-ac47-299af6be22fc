﻿
@model MHN.Entity.ServiceTemplate.IPUAdmissionForm
@{
    Layout = null;
}


<style type="text/css">
    .ui-timepicker-container {
        z-index: 9999999999 !important
    }
    /*.form-control-feedback-enrollment {
        position: absolute;
        margin-left: 86%;
        height: 25px;*/ /*alignment issue fix*/
        /*line-height: 23px;
        text-align: center;
        background: rgb(248, 248, 248) none repeat scroll 0% 0%;
        border-bottom-right-radius: 5px;
        border-top-right-radius: 5px;
        width: 25px;
        border: 1px solid #bfbfbf;
        color: #555555;
    }*/
    body.modal-open .wrapper-content .row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .panel-body {
        height: auto;
    }

    .checkbox-text label {
        display: block;
        white-space: normal;
    }

    .checkbox-text input[type=checkbox] {
        float: left;
        margin-right: 10px;
    }

    .textbx-pads {
        color: #555 !important
    }

        .textbx-pads:hover {
            color: #555 !important
        }

    .age-tds {
        width: 80px;
    }

    .fullname {
        width: 150px;
    }

    /*.EditorTemplateTime {
        position: absolute;
        right: -89px;
    }*/

    div[class^="TimeStampEditor"] col-lg-2 {
        position: absolute;
        right: -89px;
    }

    .has-search {
        pointer-events: all;
        cursor: pointer;
        padding-top: 5px;
    }

    .ACC-Download-icon {
        background: url(../Images/acc-download.png);
        width: 24px;
        height: 24px;
        float: left;
        margin-left: 5px;
        margin-top: -2px;
        margin-right: 3px;
        border: 0px;
        background-repeat: no-repeat;
    }
    /*.LeftPush {
        position: absolute;
        right: 49px;
    }*/
</style>

<form id="IPUAdmissionForm">

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 custom-no-padd-common">


        @*@using (Ajax.BeginForm("IPUFormInsertUpdate", "IPU", new AjaxOptions { HttpMethod = "POST", OnSuccess = "IpuFormSuccess" },
        new { @id = "", @class = "col-lg-12 col-md-12 col-sm-12 col-xs-12 custom-no-padd-common" }))
        {*@
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.AdmissionFormID, new { @name = "AdmissionFormID" })
        @Html.HiddenFor(m => m.ReferralPatientID, new { @name = "ReferralPatientID" })
        @Html.HiddenFor(m => m.LocalityTitle, new { @id = "LocalityTitle" })
        @Html.HiddenFor(m => m.PatientAdmissionID, new { @name = "PatientAdmissionID" })
        @Html.HiddenFor(m => m.RoomIDNew, new { @name = "RoomIDNew" })
        @Html.HiddenFor(m => m.PatientID, new { @id = "hdnPatientIDIPU" })
        @Html.HiddenFor(m => m.IsValid, new { @id = "hdnIsIPUFormValid" })
        @Html.HiddenFor(m => m.AppointmentID, new { @id = "hdnAppointmentId" })

        @Html.HiddenFor(m => m.RoomID, new { @id = "hdnAdmissionLocalityID" })
        @Html.HiddenFor(m => m.LocalityTitle, new { @id = "hdnAdmissionLocalityName" })
        @Html.HiddenFor(m => m.AssignedDoctorTXT, new { @id = "hdnAssignedDoctorTXT" })
        @Html.HiddenFor(m => m.SpecialityTXT, new { @id = "hdnSpecialityTXT" })
        @Html.HiddenFor(m => m.LocationTXT, new { @id = "hdnLocationTXT" })
        @Html.HiddenFor(m => m.RoomTXT, new { @id = "hdnRoomTXT" })
        @Html.HiddenFor(m => m.ReasonTXT, new { @id = "hdnReasonTXT" })
        @Html.HiddenFor(m => m.AdmissionSubStatusTXT, new { @id = "hdnAdmissionSubStatusTXT" })
        @Html.HiddenFor(m => m.ACC45TXT, new { @id = "hdnACC45TXT" })
        @Html.HiddenFor(m => m.Triage, new { @id = "hdnTriageTXT" })
        @Html.HiddenFor(m => m.SeenByDoctorName, new { @id = "hdnSeenByDrName" })
        @Html.HiddenFor(m => m.DischargeID, new { @id = "hdnDischargeID" })
        @Html.HiddenFor(m => m.IPUuserID, new { @id = "hdnCurrentUserID" })



        <div class="" style="margin-top:5px;">
            <div class="clearfix"></div>



            @*<div class="ModalHeading" id="PatientNameDiv"></div>*@
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <fieldset style="width:100% !important;margin:0;">
                    <legend>IPU Details</legend>
                    <div style="padding-top: 3px;padding-left:0; padding-right:0;margin-bottom: 5px;">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common" id="lblAdmission">
                            <span>Admission Date & Time</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 p-0">
                                @Html.TextBoxFor(model => model.AdmissionDate, "{0:dd/MM/yyyy}", new { @id = "admissionDateIPU", @class = " form-control Common-textBox", style = "height: 25px", placeholder = "dd/mm/yyyy", @required = "required", @maxlength = "10" })

                                <i class="glyphicon glyphicon-calendar form-control-feedback-enrollment ADclicknowBtnIPU mrgn-set" style="cursor: pointer; width: 20px; height: 25px"></i>

                            </div>

                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 form-group">
                                @Html.TextBoxFor(model => model.AdmissionTime, "{0:HH:mm }", new
                           {
                               @type = "Text",
                               @class = "timepicker form-control Common-textBox Timeonly",
                               style = "height: 25px"
                               ,
                               @id = "AdmissionTime",
                               @maxlength = "5",
                               @autoComplete = "off",
                               @required = "required"
                           })
                                @*<input type="time" class="form-control" name="AdmissionTime" value="" />*@
                                <i class="glyphicon glyphicon-time form-control-feedback-enrollment ATclicknowBtnIPU mrgn-set" style="cursor: pointer; width: 20px; height: 25px;"></i>

                            </div>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Admission Reason</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.ReasonID, new SelectList(Model.LstIPUEnumerationReason, "IPUEnumerationID", "Description"), "--Select--", new { @id = "ddlReason", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })


                            </div>
                        </div>




                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Assigned Doctor</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.AssignedDoctorID, new SelectList(Model.LstProviders, "ProviderUUID", "FirstName"), new { @id = "AssignedDoctor", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Admission Source</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.AdmissionSubStatusID, new SelectList(Model.LstAdmissionSubStatus, "AdmissionSubStatusID", "AdmissionSubStatusname"), "--Select--", new { @id = "ddlAdmissionStatus", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                            </div>

                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Speciality</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.SpecialityID, new SelectList(Model.LstReferralSpeciality, "SpecialityID", "SpecialityName"), "--Select--", new { @id = "ddlSpeciality", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Admission Type</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.AdmissionType, new List<SelectListItem>
                                            {
                                                new SelectListItem{ Text="--Select--", Value="" },
                                                new SelectListItem{ Text="Acute", Value = "Acute" },
                                                new SelectListItem{ Text="Arranged", Value = "Arranged" },


                                             }, new { @id = "ddlAdmissionType", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Location</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.LocationID, Model.localityLookupDDL, " --Select-- ", new { @id = "ddlLocation", @class = "drop-down-common form-control" })
                                @*@Html.DropDownListFor(m => m.LocationID, new SelectList(Model.LstLocation, "PracticeLocationID", "LocationName"), "--Select--", new { @id = "ddlLocation", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })*@
                            </div>
                        </div>





                    </div>
                    <div style="padding-top: 3px;padding-left:0; padding-right:0;margin-bottom: 5px;">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Patient Management</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.PatientManagement, new List<SelectListItem>
                                            {
                                                new SelectListItem{ Text="--Select--", Value="" },
                                                new SelectListItem{ Text="Inpatient", Value = "Inpatient" },
                                                new SelectListItem{ Text="Day Case", Value = "Day Case" },
                                                 new SelectListItem{ Text="Rest Home", Value = "Rest Home" },
                                                  new SelectListItem{ Text="ED", Value = "ED" },

                                             }, new { @id = "ddlPatientManagement", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Available Rooms</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.RoomID, new SelectList("", ""), "--Select--", new { @id = "ddlRoom", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Acc No. (if applicable)</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.ACC45ID, new SelectList(Model.LstAccidents, "UUID", "ACC45Number"), "--Select--", new { @id = "ddlACC45Number", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;" })

                            </div>
                        </div>
                        @*<a class="ACC-Download-icon1 pull-left" data-attr-id="0" style="font-size:18px;" onclick="IQueryACCForAllPatients()" title="Search and download All ACC Claim from ACC Database"></a>*@

                    </div>

                    <div style="padding-top: 3px;padding-left:0; padding-right:0;margin-bottom: 5px;">

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Triage</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(model => model.TriageID, new SelectList(Model.LstTriageScore, "TriageScoreID", "TriageScoreName"), "--Select--", new { @id = "ddlTriage", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Alcohol Involved</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.AlcoholInvolved, new List<SelectListItem>
                                            {
                                                new SelectListItem{ Text="--Select--", Value="" },
                                                new SelectListItem{ Text="No Alcohol Involved", Value = "No Alcohol Involved" },
                                                new SelectListItem{ Text="Unknown", Value = "Unknown" },
                                                 new SelectListItem{ Text="Yes Alcohol Involved", Value = "Yes Alcohol Involved" },
                                                  new SelectListItem{ Text="Secondary (Others consumed alcohol)", Value = "Secondary (Others consumed alcohol)" },
                                             }, new { @id = "ddlAlcoholInvolved", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })

                            </div>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Treatment Started</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 p-0">
                                @Html.TextBoxFor(model => model.Seendate, "{0:dd/MM/yyyy}", new { @id = "Seendate", @class = " form-control Common-textBox ", style = "height: 25px", placeholder = "dd/mm/yyyy", @required = "required", @maxlength = "10" })
                                <i class="glyphicon glyphicon-calendar form-control-feedback-enrollment mrgn-set " style="cursor: pointer; width: 27px; height: 25px;"></i>


                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 form-group">
                                <a class="glyphicon glyphicon-time form-control-feedback-enrollment mrgn-set  " title="Current Time" style="width: 22px; height: 25px;"></a>

                                @Html.TextBoxFor(model => model.TimeSeen, "{0:HH:mm }", new { @class = "form-control Common-textBox Timeonly", @id = "TimeSeen", @maxlength = "5", @required = "required", placeholder = "--:--" })
                                @*<input type="time" class="form-control" name="AdmissionTime" value="" />*@

                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 form-group">


                                <input type="button" class="btn btn-xs btn-primary TSclicknowBtnIPU float-left" value="NOW" style="padding: 3px 9px 3px 9px; float:left; font-size: 12px;" />

                            </div>
                        </div>



                        @* New implementation -- WCD-516 *@

                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Arrival Method</span><span style="color:red">*</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 form-group">
                            @Html.DropDownListFor(m => m.IPUAriivalMethodID, new SelectList(Model.IpuArrivalMethodList, "IPUAriivalMethodID", "ArrivalMethod"), "--Select--", new { @id = "IPUArrivalMethod", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                        </div>
                        @* ------------------------------------------------------- *@
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Seen by</span>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 form-group">
                            @Html.DropDownListFor(m => m.SeenByDoctor, new SelectList(Model.LstProviders, "ProviderUUID", "FirstName"), new { @id = "SeenbyDr", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
                        </div>

                    </div>

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-top: 3px;padding-left:0; padding-right:0">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Tasks/Comments</span>
                        </div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.TextAreaFor(m => m.Comments, new { @id = "txtComments", @class = "form-control Common-textBox", @maxlength = 200, @rows = 2 })
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-top: 3px;padding-left:0; padding-right:0">
                        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                            <span>Complaint</span>
                        </div>
                        <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12">
                            <div class="form-group">
                                @Html.TextAreaFor(m => m.compliant, new { @id = "Txtcompliant", @class = "form-control Common-textBox", @maxlength = 200, @rows = 2 })
                            </div>
                        </div>
                    </div>


                    <div vclass="clearfix"></div>
                    <di class="clearfix">
            </div>
            <div class="clearfix"></div>
            <div class="clearfix"></div>




            <hr />

            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
                <span>Family Violence Screened</span><span style="color:red">*</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12" id="rdFamilyViolenceScreenedBtn">
                <div class="form-group">
                    @Html.RadioButtonFor(model => model.FamilyViolenceScreened, "Yes", new { @id = "rdFamilyViolenceScreened", @required = "required" }) @Html.Label("Yes")
                    @Html.RadioButtonFor(model => model.FamilyViolenceScreened, "No", new { @id = "rdFamilyViolenceScreened", @required = "required" }) @Html.Label("No")
                    @*@Html.RadioButtonFor(model => model.FamilyViolenceScreened, "N/A", new { @id = "rdFamilyViolenceScreened", @required = "required" }) @Html.Label("N/A")*@
                </div>

            </div>



        </div>
        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
            <span>FV Outcome</span><span style="color:red">*</span>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <div class="form-group " id="FvoutcomeRadioBtn">

                @Html.RadioButtonFor(model => model.FVOutcome, "Positive", new { @id = "rdFVOutcome", @required = "required" }) @Html.Label("Positive")
                @Html.RadioButtonFor(model => model.FVOutcome, "Negative", new { @id = "rdFVOutcome", @required = "required" }) @Html.Label("Negative")
                @Html.RadioButtonFor(model => model.FVOutcome, "Historical", new { @id = "rdFVOutcome", @required = "required" }) @Html.Label("Historical")
            </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-top: 3px;padding-left:0; padding-right:0">
            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common hide ddlfamilyReasoning">
                <span>Reason For Not Screening</span><span style="color:red">*</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 form-group hide ddlfamilyReasoning">
                @Html.DropDownListFor(m => m.ReasonForNotScreeningID, new SelectList(Model.FamilyVoilanceList, "ReasonForNotScreeningID", "ReasonForNotScreeningName"), "--Select--", new { @id = "ddlReasonForNotScreening", @class = "form-control textbx-pads", style = "padding:2px; height:25px;border-radius:5px;", @required = "required" })
            </div>
        </div>
        <br />
        <hr />
        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
            <span>Visitor Flag</span>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <div class="form-group">
                @Html.RadioButtonFor(model => model.VisitorFlag, true) @Html.Label("Yes")
                @Html.RadioButtonFor(model => model.VisitorFlag, false) @Html.Label("No")

            </div>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
            <span>Religious Visitors</span>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
            <div class="form-group">
                @Html.RadioButtonFor(model => model.ReligiousVisitors, true) @Html.Label("Yes")
                @Html.RadioButtonFor(model => model.ReligiousVisitors, false) @Html.Label("No")

            </div>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common">
            <span>Privacy Statement</span>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
            <div class="form-group checkbox-text">
                @Html.CheckBoxFor(model => model.PrivacyStatementOne, new { @class = "form-control Common-textBox" }) @Html.Label("I wish to have information relating to my condition with held from all enquiries")
                @Html.CheckBoxFor(model => model.PrivacyStatementTwo, new { @class = "form-control Common-textBox" }) @Html.Label("I wish to have the nature of my injuries withheld from the media")

            </div>
        </div>



        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-top: 3px;padding-left:0; padding-right:0">
            <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 label-common hide">
                <span>CPC Team</span>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 hide">
                <div class="form-group">
                    @Html.CheckBoxFor(m => m.IsCpcTeam, new { @id = "chkCpcTeam", @class = "form-control Common-textBox" })
                </div>
            </div>
        </div>
        </fieldset>

        <fieldset style="width: 98% !important; margin: 11px; border: 1px solid #0087c4; padding: 0px;">
            <legend>Allergies/Adverse reactions </legend>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 DivScrol style-scroll" style="height:200px; overflow-y:auto" id="divAllergies">


            </div>
        </fieldset>

    </div>

            <div class="clearfix"></div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-top: 15px;padding-left:0; padding-right:0">
                <div class="pull-right" style="padding-right:6px;">
                    @*<input id="btnCreateAdmissionForm" type="button" class="btn btn-xs btn-success" value="Save" style="padding: 3px 19px 3px 18px; font-size: 14px;" />*@
                    @*<input type="button" id="" class="btn btn-xs btn-success DismisIPUModel" placeholder="Close" style="padding: 3px 19px 3px 18px; font-size: 14px;" />*@
                    <a id="btnCreateAdmissionForm" class="btn btn-xs btn-success" value="Save" style="padding: 3px 19px 3px 18px; font-size: 14px;"> Save</a>

                    <a id="" class="btn btn-xs btn-success DismisIPUModel" style="padding: 3px 19px 3px 18px; font-size: 14px;"> Close</a>

                </div>
            </div>
        </div>
        <input type="hidden" value="@Model.AdmissionDate.ToShortDateString()" id="ADmsiondate" />
    </div>
</form>


<div class="clearfix"></div>

