namespace IPU.API.DTOs
{
    /// <summary>
    /// DTO for returning locality data with count information
    /// </summary>
    public class LocalityDto
    {
        public int LocalityID { get; set; }
        public string LocalityTitle { get; set; } = string.Empty;
        public string LocalityCode { get; set; } = string.Empty;
        public string? LocalityDescription { get; set; }
        public int LocalityTypeID { get; set; }
        public string LocalityTypeName { get; set; } = string.Empty;
        public int? ParentLocalityID { get; set; }
        public string? ParentLocalityTitle { get; set; }
        public bool IsActive { get; set; }
        public bool IsOccupied { get; set; }
        public string? Facilities { get; set; }
        public bool? IsMultipleRoom { get; set; }
        public bool? IsHomeLeave { get; set; }
        public int? SequenceNo { get; set; }
        public DateTime InsertedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        // Count properties
        public int ChildCount { get; set; } // Number of child localities (e.g., wards in building)
    }

    /// <summary>
    /// DTO for creating new locality
    /// </summary>
    public class CreateLocalityDto
    {
        public string LocalityTitle { get; set; } = string.Empty;
        public string LocalityCode { get; set; } = string.Empty;
        public string? LocalityDescription { get; set; }
        public int LocalityTypeID { get; set; }
        public int? ParentLocalityID { get; set; }
        public string? Facilities { get; set; }
        public bool? IsMultipleRoom { get; set; }
        public bool? IsHomeLeave { get; set; }
        public int? SequenceNo { get; set; }
        public int? PracticeID { get; set; }
        public int? PracticeLocationID { get; set; }
    }

    /// <summary>
    /// DTO for updating locality
    /// </summary>
    public class UpdateLocalityDto
    {
        public string LocalityTitle { get; set; } = string.Empty;
        public string LocalityCode { get; set; } = string.Empty;
        public string? LocalityDescription { get; set; }
        public string? Facilities { get; set; }
        public bool? IsMultipleRoom { get; set; }
        public bool? IsHomeLeave { get; set; }
        public int? SequenceNo { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Simplified DTO for creating locality with minimal input
    /// </summary>
    public class SimpleCreateLocalityDto
    {
        public string LocalityTitle { get; set; } = string.Empty;
        public string? ParentLocalityName { get; set; } // Name of parent locality (for wards/rooms)
        public string? LocalityDescription { get; set; }
    }

    /// <summary>
    /// DTO for updating room's ward assignment
    /// </summary>
    public class UpdateRoomWardDto
    {
        public string NewWardName { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for updating ward's building assignment
    /// </summary>
    public class UpdateWardBuildingDto
    {
        public string NewBuildingName { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for updating locality description only
    /// </summary>
    public class UpdateDescriptionDto
    {
        public string? LocalityDescription { get; set; }
    }

    /// <summary>
    /// Generic API response wrapper
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public static ApiResponse<T> SuccessResponse(T data, string message = "Success")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static ApiResponse<T> ErrorResponse(string message)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Data = default(T)
            };
        }
    }
}
