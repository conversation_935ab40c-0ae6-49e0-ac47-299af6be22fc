namespace IPU.API.Models
{
    /// <summary>
    /// Represents a patient discharge from [Appointment].[tblDischarge] table
    /// </summary>
    public class Discharge
    {
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
        public int? AppointmentID { get; set; }
        public int DischargeID { get; set; }
        public string? DischargeKesslerRisk { get; set; }
        public int? DischargeKesslerScore { get; set; }
        public int DischargeReasonID { get; set; }
        public int? DischargeStatusID { get; set; }
        public DateTime InsertedAt { get; set; }
        public int InsertedBy { get; set; }
        public string? IntakeKesslerRisk { get; set; }
        public int? IntakeKesslerScore { get; set; }
        public bool IsActive { get; set; }
        public bool? IsConfidentialDischargeKesslerRisk { get; set; }
        public bool? IsConfidentialIntakeKesslerRisk { get; set; }
        public bool? IsConfidentialReason { get; set; }
        public bool IsDeleted { get; set; }
        public bool? IsSurveyProvided { get; set; }
        public int? PatientID { get; set; }
        public int TemplateGroupId { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime? EpisodeEndDate { get; set; }
        public DateTime? DischargeDate { get; set; }
        public int? DischargeEndModeID { get; set; }
        public int? DischargeEndAccomadationID { get; set; }
        public int? EpisodeEndSupportID { get; set; }
        public DateTime? MedicalDischargeDate { get; set; }
        public int? DischargeConvenienceID { get; set; }
        public DateTime? PhysicalDischargeDate { get; set; }
        public int? DischargeDelayID { get; set; }
        public string? DischargingCarer { get; set; }
        public DateTime? medicaldischargetime { get; set; }
        public DateTime? physicaldischargetime { get; set; }
        public string? Outcome { get; set; }
        public string? DiagnosisID { get; set; }
        public int? iPMUniqueID { get; set; }
        public int? AssignedDoctorID { get; set; }
        public DateTime? Timeseen { get; set; }
        public int? SeenByDoctorID { get; set; }
        public string? SeenByDoctorName { get; set; }
        public string? ConceptID { get; set; }
        public string? DiseaseName { get; set; }
        public DateTime? Seendate { get; set; }
        public bool? IsParked { get; set; } // Key field for medical discharge status
        public string? DischargedDestination { get; set; }
        public int? ReferralPatientID { get; set; }
        public int? EpisodeEndAccommodationID { get; set; }
        public DateTime? EOCEndDate { get; set; }
        public int? TeamID { get; set; }
        public int? EpisodeEndModeID { get; set; }

        // Navigation properties
        public Patient? Patient { get; set; }
        public List<PatientAdmission> PatientAdmissions { get; set; } = new List<PatientAdmission>();
    }
}
