using IPU.API.Models;

namespace IPU.API.Data
{
    /// <summary>
    /// Repository interface for locality operations
    /// </summary>
    public interface ILocalityRepository
    {
        // Get operations
        Task<List<Locality>> GetAllLocalitiesByTypeAsync(int localityTypeId);
        Task<Locality?> GetLocalityByIdAsync(int localityId);
        Task<List<Locality>> GetChildLocalitiesAsync(int parentLocalityId);
        Task<int> GetChildCountAsync(int parentLocalityId);
        
        // CRUD operations
        Task<int> CreateLocalityAsync(Locality locality);
        Task<bool> UpdateLocalityAsync(Locality locality);
        Task<bool> DeleteLocalityAsync(int localityId);
        
        // Specific queries
        Task<bool> LocalityExistsAsync(int localityId);
        Task<bool> LocalityCodeExistsAsync(string localityCode, int? excludeId = null);
        Task<List<Locality>> GetAvailableRoomsAsync(); // For rooms that are not occupied
        Task<List<LocalityType>> GetAllLocalityTypesAsync();

        // Search functionality
        Task<List<Locality>> SearchLocalitiesByNameAsync(string searchTerm, int? localityTypeId = null);
        Task<List<Locality>> SearchBuildingsByNameAsync(string searchTerm);
        Task<List<Locality>> SearchWardsByNameAsync(string searchTerm);
        Task<List<Locality>> SearchRoomsByNameAsync(string searchTerm);

        // Update by name functionality
        Task<Locality?> GetLocalityByNameAsync(string localityName, int localityTypeId);
        Task<bool> UpdateLocalityByNameAsync(string localityName, int localityTypeId, Locality locality);

        // Delete by name functionality
        Task<bool> DeleteLocalityByNameAsync(string localityName, int localityTypeId);

        // Update by code functionality
        Task<Locality?> GetLocalityByCodeAsync(string localityCode, int localityTypeId);
        Task<bool> UpdateLocalityByCodeAsync(string localityCode, int localityTypeId, Locality locality);

        // Parent relationship updates
        Task<bool> UpdateRoomWardAsync(int roomId, int newWardId);
        Task<bool> UpdateWardBuildingAsync(int wardId, int newBuildingId);

        // Description update functionality
        Task<bool> UpdateLocalityDescriptionAsync(int localityId, string description);
        Task<bool> UpdateLocalityDescriptionByNameAsync(string localityName, int localityTypeId, string description);
        Task<bool> UpdateLocalityDescriptionByCodeAsync(string localityCode, int localityTypeId, string description);
    }
}
