namespace IPU.API.DTOs
{
    /// <summary>
    /// DTO for patient information
    /// </summary>
    public class PatientDto
    {
        public int PatientID { get; set; }
        public string? ChartNumber { get; set; }
        public bool IsAdmitted { get; set; }
        public bool IsActive { get; set; }
        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? FamilyName { get; set; }
        public string? FullName { get; set; }
        public string? PreferredName { get; set; }
        public DateTime? DOB { get; set; }
        public string? CellNumber { get; set; }
        public string? Email { get; set; }
        public string? NHINumber { get; set; }
        public int? GenderID { get; set; }
        public DateTime? InsertedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO for patient admission information
    /// </summary>
    public class PatientAdmissionDto
    {
        public int PatientAdmissionID { get; set; }
        public int? PatientID { get; set; }
        public string? PatientName { get; set; }
        public string? ChartNumber { get; set; }
        public DateTime? AdmissionDate { get; set; }
        public DateTime? AdmissionTime { get; set; }
        public int? LocalityID { get; set; }
        public string? RoomName { get; set; }
        public string? RoomCode { get; set; }
        public int? AssignedNurseID { get; set; }
        public int? AssignedDoctorID { get; set; }
        public string? AdmissionType { get; set; }
        public string? Comments { get; set; }
        public bool IsActive { get; set; }
        public bool? IsParked { get; set; }
        public int? DischargeID { get; set; }
        public DateTime? InsertedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO for discharge information
    /// </summary>
    public class DischargeDto
    {
        public int DischargeID { get; set; }
        public int? PatientID { get; set; }
        public string? PatientName { get; set; }
        public string? ChartNumber { get; set; }
        public DateTime? DischargeDate { get; set; }
        public DateTime? MedicalDischargeDate { get; set; }
        public DateTime? PhysicalDischargeDate { get; set; }
        public int DischargeReasonID { get; set; }
        public string? Outcome { get; set; }
        public string? DischargedDestination { get; set; }
        public bool? IsParked { get; set; }
        public bool IsActive { get; set; }
        public DateTime? InsertedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO for patient statistics
    /// </summary>
    public class PatientStatsDto
    {
        public int TotalInpatients { get; set; }
        public int AdmittedPatients { get; set; }
        public int DischargedToday { get; set; }
        public int AdmittedToday { get; set; }
        public int MedicallyDischargedPatients { get; set; }
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// DTO for patient admission status check
    /// </summary>
    public class PatientAdmissionStatusDto
    {
        public string PatientName { get; set; } = string.Empty;
        public bool IsAdmitted { get; set; }
        public DateTime? AdmissionDate { get; set; }
        public string? RoomName { get; set; }
        public string? AdmissionType { get; set; }
        public bool? IsParked { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for room allocation information
    /// </summary>
    public class RoomAllocationDto
    {
        public int PatientAdmissionID { get; set; }
        public int PatientID { get; set; }
        public string? PatientName { get; set; }
        public string? ChartNumber { get; set; }
        public DateTime? AdmissionDate { get; set; }
        public int? CurrentRoomID { get; set; }
        public string? CurrentRoomName { get; set; }
        public string? CurrentRoomCode { get; set; }
        public bool? IsCurrentRoomOccupied { get; set; }
    }

    /// <summary>
    /// DTO for updating patient room allocation
    /// </summary>
    public class UpdateRoomAllocationDto
    {
        public int NewRoomID { get; set; }
        public string? Comments { get; set; }
    }

    /// <summary>
    /// DTO for available rooms
    /// </summary>
    public class AvailableRoomDto
    {
        public int LocalityID { get; set; }
        public string LocalityTitle { get; set; } = string.Empty;
        public string LocalityCode { get; set; } = string.Empty;
        public string? LocalityDescription { get; set; }
        public bool IsOccupied { get; set; }
        public string? WardName { get; set; }
        public string? BuildingName { get; set; }
    }
}
