-- =============================================
-- Required Stored Procedures for IPU Configuration API
-- These procedures need to be created in your database
-- =============================================

-- 1. Get all localities by type (Buildings, Wards, Rooms, Beds)
CREATE PROCEDURE [IPU].[uspGetLocalitiesByType]
    @LocalityTypeID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsA<PERSON>,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.<PERSON>,
        l.<PERSON>Occupied,
        l.Fac<PERSON>,
        l.<PERSON>ultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.LocalityTypeID = @LocalityTypeID 
        AND l.IsDeleted = 0
    ORDER BY l.SequenceNo, l.LocalityTitle;
END
GO

-- 2. Get locality by ID
CREATE PROCEDURE [IPU].[uspGetLocalityById]
    @LocalityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.LocalityID = @LocalityID 
        AND l.IsDeleted = 0;
END
GO

-- 3. Get child localities (e.g., wards in a building, rooms in a ward)
CREATE PROCEDURE [IPU].[uspGetChildLocalities]
    @ParentLocalityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.ParentLocalityID = @ParentLocalityID 
        AND l.IsDeleted = 0
    ORDER BY l.SequenceNo, l.LocalityTitle;
END
GO

-- 4. Get child count for a locality
CREATE PROCEDURE [IPU].[uspGetChildCount]
    @ParentLocalityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) 
    FROM [IPU].[tblLocality] 
    WHERE ParentLocalityID = @ParentLocalityID 
        AND IsDeleted = 0 
        AND IsActive = 1;
END
GO

-- 5. Create new locality
CREATE PROCEDURE [IPU].[uspCreateLocality]
    @LocalityTitle NVARCHAR(200),
    @LocalityCode NVARCHAR(20),
    @LocalityDescription NVARCHAR(300) = NULL,
    @LocalityTypeID INT,
    @ParentLocalityID INT = NULL,
    @PracticeID INT = NULL,
    @PracticeLocationID INT = NULL,
    @SequenceNo INT = NULL,
    @Facilities NVARCHAR(100) = NULL,
    @IsMultipleRoom BIT = NULL,
    @IsHomeLeave BIT = NULL,
    @InsertedBy INT,
    @UserLoggingID BIGINT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @NewLocalityID INT;
    
    -- Get next LocalityID (you may need to adjust this based on your ID generation strategy)
    SELECT @NewLocalityID = ISNULL(MAX(LocalityID), 0) + 1 FROM [IPU].[tblLocality];
    
    INSERT INTO [IPU].[tblLocality] (
        LocalityID,
        LocalityTitle,
        LocalityCode,
        LocalityDescription,
        LocalityTypeID,
        ParentLocalityID,
        IsActive,
        IsDeleted,
        InsertedBy,
        UpdatedBy,
        InsertedAt,
        UpdatedAt,
        UserLoggingID,
        PracticeID,
        PracticeLocationID,
        SequenceNo,
        IsOccupied,
        Facilities,
        IsMultipleRoom,
        IsHomeLeave
    )
    VALUES (
        @NewLocalityID,
        @LocalityTitle,
        @LocalityCode,
        @LocalityDescription,
        @LocalityTypeID,
        @ParentLocalityID,
        1, -- IsActive
        0, -- IsDeleted
        @InsertedBy,
        @InsertedBy, -- UpdatedBy same as InsertedBy initially
        GETDATE(),
        GETDATE(),
        @UserLoggingID,
        @PracticeID,
        @PracticeLocationID,
        @SequenceNo,
        0, -- IsOccupied default to false
        @Facilities,
        @IsMultipleRoom,
        @IsHomeLeave
    );
    
    SELECT @NewLocalityID as NewLocalityID;
END
GO

-- 6. Update locality
CREATE PROCEDURE [IPU].[uspUpdateLocality]
    @LocalityID INT,
    @LocalityTitle NVARCHAR(200),
    @LocalityCode NVARCHAR(20),
    @LocalityDescription NVARCHAR(300) = NULL,
    @SequenceNo INT = NULL,
    @Facilities NVARCHAR(100) = NULL,
    @IsMultipleRoom BIT = NULL,
    @IsHomeLeave BIT = NULL,
    @IsActive BIT,
    @UpdatedBy INT,
    @UserLoggingID BIGINT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE [IPU].[tblLocality] 
    SET 
        LocalityTitle = @LocalityTitle,
        LocalityCode = @LocalityCode,
        LocalityDescription = @LocalityDescription,
        SequenceNo = @SequenceNo,
        Facilities = @Facilities,
        IsMultipleRoom = @IsMultipleRoom,
        IsHomeLeave = @IsHomeLeave,
        IsActive = @IsActive,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETDATE(),
        UserLoggingID = @UserLoggingID
    WHERE LocalityID = @LocalityID;
    
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 7. Delete locality (soft delete)
CREATE PROCEDURE [IPU].[uspDeleteLocality]
    @LocalityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE [IPU].[tblLocality] 
    SET 
        IsDeleted = 1,
        UpdatedAt = GETDATE()
    WHERE LocalityID = @LocalityID;
    
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 8. Check if locality exists
CREATE PROCEDURE [IPU].[uspLocalityExists]
    @LocalityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT CASE WHEN EXISTS(
        SELECT 1 FROM [IPU].[tblLocality] 
        WHERE LocalityID = @LocalityID AND IsDeleted = 0
    ) THEN 1 ELSE 0 END as Exists;
END
GO

-- 9. Check if locality code exists
CREATE PROCEDURE [IPU].[uspLocalityCodeExists]
    @LocalityCode NVARCHAR(20),
    @ExcludeID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT CASE WHEN EXISTS(
        SELECT 1 FROM [IPU].[tblLocality] 
        WHERE LocalityCode = @LocalityCode 
            AND IsDeleted = 0
            AND (@ExcludeID IS NULL OR LocalityID != @ExcludeID)
    ) THEN 1 ELSE 0 END as Exists;
END
GO

-- 10. Get available rooms (not occupied)
CREATE PROCEDURE [IPU].[uspGetAvailableRooms]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.LocalityTypeID = 3 -- Room type
        AND l.IsDeleted = 0
        AND l.IsActive = 1
        AND l.IsOccupied = 0
    ORDER BY l.SequenceNo, l.LocalityTitle;
END
GO

-- 11. Get all locality types
CREATE PROCEDURE [IPU].[uspGetAllLocalityTypes]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        LocalityTypeID,
        LocalityTypeName,
        IsActive,
        IsDeleted,
        InsertedBy,
        UpdatedBy,
        InsertedAt,
        UpdatedAt,
        UserLoggingID,
        AnalysisTimestamp
    FROM [IPU].[tblLocalityType]
    WHERE IsDeleted = 0
    ORDER BY LocalityTypeID;
END
GO

-- 12. Search localities by name
CREATE PROCEDURE [IPU].[uspSearchLocalitiesByName]
    @SearchTerm NVARCHAR(200),
    @LocalityTypeID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.IsDeleted = 0
        AND l.IsActive = 1
        AND (
            l.LocalityTitle LIKE '%' + @SearchTerm + '%'
            OR l.LocalityCode LIKE '%' + @SearchTerm + '%'
            OR l.LocalityDescription LIKE '%' + @SearchTerm + '%'
        )
        AND (@LocalityTypeID IS NULL OR l.LocalityTypeID = @LocalityTypeID)
    ORDER BY
        l.LocalityTypeID,
        CASE
            WHEN l.LocalityTitle LIKE @SearchTerm + '%' THEN 1  -- Starts with search term
            WHEN l.LocalityTitle LIKE '%' + @SearchTerm + '%' THEN 2  -- Contains search term
            WHEN l.LocalityCode LIKE @SearchTerm + '%' THEN 3  -- Code starts with search term
            WHEN l.LocalityCode LIKE '%' + @SearchTerm + '%' THEN 4  -- Code contains search term
            ELSE 5
        END,
        l.LocalityTitle;
END
GO

-- 13. Get locality by name and type
CREATE PROCEDURE [IPU].[uspGetLocalityByName]
    @LocalityName NVARCHAR(200),
    @LocalityTypeID INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.LocalityTitle = @LocalityName
        AND l.LocalityTypeID = @LocalityTypeID
        AND l.IsDeleted = 0
        AND l.IsActive = 1;
END
GO

-- 14. Update locality by name and type
CREATE PROCEDURE [IPU].[uspUpdateLocalityByName]
    @LocalityName NVARCHAR(200),
    @LocalityTypeID INT,
    @NewLocalityTitle NVARCHAR(200),
    @NewLocalityCode NVARCHAR(20),
    @LocalityDescription NVARCHAR(300) = NULL,
    @SequenceNo INT = NULL,
    @Facilities NVARCHAR(100) = NULL,
    @IsMultipleRoom BIT = NULL,
    @IsHomeLeave BIT = NULL,
    @IsActive BIT,
    @UpdatedBy INT,
    @UserLoggingID BIGINT
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        LocalityTitle = @NewLocalityTitle,
        LocalityCode = @NewLocalityCode,
        LocalityDescription = @LocalityDescription,
        SequenceNo = @SequenceNo,
        Facilities = @Facilities,
        IsMultipleRoom = @IsMultipleRoom,
        IsHomeLeave = @IsHomeLeave,
        IsActive = @IsActive,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETDATE(),
        UserLoggingID = @UserLoggingID
    WHERE LocalityTitle = @LocalityName
        AND LocalityTypeID = @LocalityTypeID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 15. Delete locality by name and type (soft delete)
CREATE PROCEDURE [IPU].[uspDeleteLocalityByName]
    @LocalityName NVARCHAR(200),
    @LocalityTypeID INT
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        IsDeleted = 1,
        UpdatedAt = GETDATE()
    WHERE LocalityTitle = @LocalityName
        AND LocalityTypeID = @LocalityTypeID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 16. Get locality by code and type
CREATE PROCEDURE [IPU].[uspGetLocalityByCode]
    @LocalityCode NVARCHAR(20),
    @LocalityTypeID INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityCode,
        l.LocalityDescription,
        l.LocalityTypeID,
        l.ParentLocalityID,
        l.IsActive,
        l.IsDeleted,
        l.InsertedBy,
        l.UpdatedBy,
        l.InsertedAt,
        l.UpdatedAt,
        l.UserLoggingID,
        l.AnalysisTimestamp,
        l.PracticeID,
        l.PracticeLocationID,
        l.SequenceNo,
        l.IsOccupied,
        l.Facilities,
        l.IsMultipleRoom,
        l.IsHomeLeave,
        lt.LocalityTypeName,
        parent.LocalityTitle as ParentTitle
    FROM [IPU].[tblLocality] l
    LEFT JOIN [IPU].[tblLocalityType] lt ON l.LocalityTypeID = lt.LocalityTypeID
    LEFT JOIN [IPU].[tblLocality] parent ON l.ParentLocalityID = parent.LocalityID
    WHERE l.LocalityCode = @LocalityCode
        AND l.LocalityTypeID = @LocalityTypeID
        AND l.IsDeleted = 0
        AND l.IsActive = 1;
END
GO

-- 17. Update locality by code and type
CREATE PROCEDURE [IPU].[uspUpdateLocalityByCode]
    @LocalityCode NVARCHAR(20),
    @LocalityTypeID INT,
    @NewLocalityTitle NVARCHAR(200),
    @NewLocalityCode NVARCHAR(20),
    @LocalityDescription NVARCHAR(300) = NULL,
    @SequenceNo INT = NULL,
    @Facilities NVARCHAR(100) = NULL,
    @IsMultipleRoom BIT = NULL,
    @IsHomeLeave BIT = NULL,
    @IsActive BIT,
    @UpdatedBy INT,
    @UserLoggingID BIGINT
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        LocalityTitle = @NewLocalityTitle,
        LocalityCode = @NewLocalityCode,
        LocalityDescription = @LocalityDescription,
        SequenceNo = @SequenceNo,
        Facilities = @Facilities,
        IsMultipleRoom = @IsMultipleRoom,
        IsHomeLeave = @IsHomeLeave,
        IsActive = @IsActive,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETDATE(),
        UserLoggingID = @UserLoggingID
    WHERE LocalityCode = @LocalityCode
        AND LocalityTypeID = @LocalityTypeID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 18. Update room's ward assignment
CREATE PROCEDURE [IPU].[uspUpdateRoomWard]
    @RoomID INT,
    @NewWardID INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Validate that the room exists and is a room type
    IF NOT EXISTS (SELECT 1 FROM [IPU].[tblLocality] WHERE LocalityID = @RoomID AND LocalityTypeID = 3 AND IsDeleted = 0)
    BEGIN
        RAISERROR('Room not found or invalid', 16, 1);
        RETURN;
    END

    -- Validate that the new ward exists and is a ward type
    IF NOT EXISTS (SELECT 1 FROM [IPU].[tblLocality] WHERE LocalityID = @NewWardID AND LocalityTypeID = 2 AND IsDeleted = 0)
    BEGIN
        RAISERROR('Ward not found or invalid', 16, 1);
        RETURN;
    END

    UPDATE [IPU].[tblLocality]
    SET
        ParentLocalityID = @NewWardID,
        UpdatedAt = GETDATE()
    WHERE LocalityID = @RoomID;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 19. Update ward's building assignment
CREATE PROCEDURE [IPU].[uspUpdateWardBuilding]
    @WardID INT,
    @NewBuildingID INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Validate that the ward exists and is a ward type
    IF NOT EXISTS (SELECT 1 FROM [IPU].[tblLocality] WHERE LocalityID = @WardID AND LocalityTypeID = 2 AND IsDeleted = 0)
    BEGIN
        RAISERROR('Ward not found or invalid', 16, 1);
        RETURN;
    END

    -- Validate that the new building exists and is a building type
    IF NOT EXISTS (SELECT 1 FROM [IPU].[tblLocality] WHERE LocalityID = @NewBuildingID AND LocalityTypeID = 1 AND IsDeleted = 0)
    BEGIN
        RAISERROR('Building not found or invalid', 16, 1);
        RETURN;
    END

    UPDATE [IPU].[tblLocality]
    SET
        ParentLocalityID = @NewBuildingID,
        UpdatedAt = GETDATE()
    WHERE LocalityID = @WardID;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 20. Update locality description by ID
CREATE PROCEDURE [IPU].[uspUpdateLocalityDescription]
    @LocalityID INT,
    @LocalityDescription NVARCHAR(300) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        LocalityDescription = @LocalityDescription,
        UpdatedAt = GETDATE()
    WHERE LocalityID = @LocalityID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 21. Update locality description by name and type
CREATE PROCEDURE [IPU].[uspUpdateLocalityDescriptionByName]
    @LocalityName NVARCHAR(200),
    @LocalityTypeID INT,
    @LocalityDescription NVARCHAR(300) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        LocalityDescription = @LocalityDescription,
        UpdatedAt = GETDATE()
    WHERE LocalityTitle = @LocalityName
        AND LocalityTypeID = @LocalityTypeID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- 22. Update locality description by code and type
CREATE PROCEDURE [IPU].[uspUpdateLocalityDescriptionByCode]
    @LocalityCode NVARCHAR(20),
    @LocalityTypeID INT,
    @LocalityDescription NVARCHAR(300) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [IPU].[tblLocality]
    SET
        LocalityDescription = @LocalityDescription,
        UpdatedAt = GETDATE()
    WHERE LocalityCode = @LocalityCode
        AND LocalityTypeID = @LocalityTypeID
        AND IsDeleted = 0;

    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =============================================
-- PATIENT MANAGEMENT STORED PROCEDURES
-- =============================================

-- 23. Get total number of inpatients (IsAdmitted = 1)
CREATE PROCEDURE [IPU].[uspGetTotalInpatients]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT COUNT(*) as TotalInpatients
    FROM [Profile].[tblPatient]
    WHERE IsAdmitted = 1
        AND pIsActive = 1
        AND pIsDeleted = 0;
END
GO

-- 24. Get number of admitted patients in IPU (IsAdmitted = 1 and has admission record)
CREATE PROCEDURE [IPU].[uspGetAdmittedPatientsCount]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT COUNT(DISTINCT p.PatientID) as AdmittedPatientsCount
    FROM [Profile].[tblPatient] p
    INNER JOIN [IPU].[tblPatientAdmission] pa ON p.PatientID = pa.PatientID
    WHERE p.IsAdmitted = 1
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
        AND pa.IsActive = 1
        AND pa.IsDeleted = 0;
END
GO

-- 25. Check patient admission status by name
CREATE PROCEDURE [IPU].[uspCheckPatientAdmissionStatus]
    @PatientName NVARCHAR(200)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        p.IsAdmitted,
        pa.AdmissionDate,
        l.LocalityTitle as RoomName,
        pa.AdmissionType,
        pa.IsParked
    FROM [Profile].[tblPatient] p
    LEFT JOIN [IPU].[tblPatientAdmission] pa ON p.PatientID = pa.PatientID
        AND pa.IsActive = 1 AND pa.IsDeleted = 0
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE (p.pFullName LIKE '%' + @PatientName + '%'
           OR p.pFirstName LIKE '%' + @PatientName + '%'
           OR p.pFamilyName LIKE '%' + @PatientName + '%'
           OR p.pPreferredName LIKE '%' + @PatientName + '%')
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0;
END
GO

-- 26. Get discharged patients by date
CREATE PROCEDURE [IPU].[uspGetDischargedPatientsByDate]
    @DischargeDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        d.DischargeID,
        d.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        d.DischargeDate,
        d.MedicalDischargeDate,
        d.PhysicalDischargeDate,
        d.DischargeReasonID,
        d.Outcome,
        d.DischargedDestination,
        d.IsParked,
        d.IsActive,
        d.InsertedAt,
        d.UpdatedAt
    FROM [Appointment].[tblDischarge] d
    INNER JOIN [Profile].[tblPatient] p ON d.PatientID = p.PatientID
    WHERE CAST(d.DischargeDate AS DATE) = @DischargeDate
        AND d.IsActive = 1
        AND d.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
    ORDER BY d.DischargeDate DESC;
END
GO

-- 27. Get admitted patients by date
CREATE PROCEDURE [IPU].[uspGetAdmittedPatientsByDate]
    @AdmissionDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        pa.PatientAdmissionID,
        pa.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        pa.AdmissionDate,
        pa.AdmissionTime,
        pa.LocalityID,
        l.LocalityTitle as RoomName,
        l.LocalityCode as RoomCode,
        pa.AssignedNurseID,
        pa.AssignedDoctorID,
        pa.AdmissionType,
        pa.Comments,
        pa.IsActive,
        pa.IsParked,
        pa.DischargeID,
        pa.InsertedAt,
        pa.UpdatedAt
    FROM [IPU].[tblPatientAdmission] pa
    INNER JOIN [Profile].[tblPatient] p ON pa.PatientID = p.PatientID
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE CAST(pa.AdmissionDate AS DATE) = @AdmissionDate
        AND pa.IsActive = 1
        AND pa.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
    ORDER BY pa.AdmissionDate DESC;
END
GO

-- 28. Get medically discharged patients (IsAdmitted = 1, IsParked = 1, IsActive = 1)
CREATE PROCEDURE [IPU].[uspGetMedicallyDischargedPatients]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        pa.PatientAdmissionID,
        pa.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        pa.AdmissionDate,
        pa.AdmissionTime,
        pa.LocalityID,
        l.LocalityTitle as RoomName,
        l.LocalityCode as RoomCode,
        pa.AssignedNurseID,
        pa.AssignedDoctorID,
        pa.AdmissionType,
        pa.Comments,
        pa.IsActive,
        pa.IsParked,
        pa.DischargeID,
        pa.InsertedAt,
        pa.UpdatedAt
    FROM [IPU].[tblPatientAdmission] pa
    INNER JOIN [Profile].[tblPatient] p ON pa.PatientID = p.PatientID
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE p.IsAdmitted = 1
        AND pa.IsParked = 1
        AND pa.IsActive = 1
        AND pa.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
    ORDER BY pa.AdmissionDate DESC;
END
GO

-- 29. Get today's admitted patients room allocation
CREATE PROCEDURE [IPU].[uspGetTodayAdmittedPatientsRoomAllocation]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        pa.PatientAdmissionID,
        pa.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        pa.AdmissionDate,
        pa.LocalityID as CurrentRoomID,
        l.LocalityTitle as CurrentRoomName,
        l.LocalityCode as CurrentRoomCode,
        l.IsOccupied as IsCurrentRoomOccupied
    FROM [IPU].[tblPatientAdmission] pa
    INNER JOIN [Profile].[tblPatient] p ON pa.PatientID = p.PatientID
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE CAST(pa.AdmissionDate AS DATE) = CAST(GETDATE() AS DATE)
        AND pa.IsActive = 1
        AND pa.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
    ORDER BY pa.AdmissionDate DESC;
END
GO

-- 30. Get available rooms (IsOccupied = 0)
CREATE PROCEDURE [IPU].[uspGetAvailableRooms]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        room.LocalityID,
        room.LocalityTitle,
        room.LocalityCode,
        room.LocalityDescription,
        room.IsOccupied,
        ward.LocalityTitle as WardName,
        building.LocalityTitle as BuildingName
    FROM [IPU].[tblLocality] room
    LEFT JOIN [IPU].[tblLocality] ward ON room.ParentLocalityID = ward.LocalityID
    LEFT JOIN [IPU].[tblLocality] building ON ward.ParentLocalityID = building.LocalityID
    WHERE room.LocalityTypeID = 3 -- Room type
        AND room.IsOccupied = 0
        AND room.IsActive = 1
        AND room.IsDeleted = 0
    ORDER BY building.LocalityTitle, ward.LocalityTitle, room.LocalityTitle;
END
GO

-- 31. Update patient room allocation
CREATE PROCEDURE [IPU].[uspUpdatePatientRoomAllocation]
    @PatientAdmissionID INT,
    @NewRoomID INT,
    @Comments NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- Get current room ID
        DECLARE @CurrentRoomID INT;
        SELECT @CurrentRoomID = LocalityID
        FROM [IPU].[tblPatientAdmission]
        WHERE PatientAdmissionID = @PatientAdmissionID;

        -- Check if new room is available
        IF NOT EXISTS (SELECT 1 FROM [IPU].[tblLocality]
                      WHERE LocalityID = @NewRoomID
                        AND LocalityTypeID = 3
                        AND IsOccupied = 0
                        AND IsActive = 1
                        AND IsDeleted = 0)
        BEGIN
            RAISERROR('Room is not available', 16, 1);
            RETURN;
        END

        -- Update old room to not occupied (if exists)
        IF @CurrentRoomID IS NOT NULL
        BEGIN
            UPDATE [IPU].[tblLocality]
            SET IsOccupied = 0, UpdatedAt = GETDATE()
            WHERE LocalityID = @CurrentRoomID;
        END

        -- Update new room to occupied
        UPDATE [IPU].[tblLocality]
        SET IsOccupied = 1, UpdatedAt = GETDATE()
        WHERE LocalityID = @NewRoomID;

        -- Update patient admission record
        UPDATE [IPU].[tblPatientAdmission]
        SET
            LocalityID = @NewRoomID,
            Comments = ISNULL(@Comments, Comments),
            UpdatedAt = GETDATE()
        WHERE PatientAdmissionID = @PatientAdmissionID;

        COMMIT TRANSACTION;
        SELECT @@ROWCOUNT as RowsAffected;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- 32. Get patient by ID
CREATE PROCEDURE [IPU].[uspGetPatientById]
    @PatientID INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        p.PatientID,
        p.ChartNumber,
        p.IsAdmitted,
        p.pFirstName,
        p.pMiddleName,
        p.pFamilyName,
        p.pFullName,
        p.pPreferredName,
        p.pDOB,
        p.pCellNumber,
        p.pEmail,
        p.pNHINumber,
        p.pGenderID,
        p.pIsActive,
        p.pIsDeleted,
        p.InsertedAt,
        p.UpdatedAt
    FROM [Profile].[tblPatient] p
    WHERE p.PatientID = @PatientID
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0;
END
GO

-- 33. Get patient by name
CREATE PROCEDURE [IPU].[uspGetPatientByName]
    @PatientName NVARCHAR(200)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        p.PatientID,
        p.ChartNumber,
        p.IsAdmitted,
        p.pFirstName,
        p.pMiddleName,
        p.pFamilyName,
        p.pFullName,
        p.pPreferredName,
        p.pDOB,
        p.pCellNumber,
        p.pEmail,
        p.pNHINumber,
        p.pGenderID,
        p.pIsActive,
        p.pIsDeleted,
        p.InsertedAt,
        p.UpdatedAt
    FROM [Profile].[tblPatient] p
    WHERE (p.pFullName LIKE '%' + @PatientName + '%'
           OR p.pFirstName LIKE '%' + @PatientName + '%'
           OR p.pFamilyName LIKE '%' + @PatientName + '%'
           OR p.pPreferredName LIKE '%' + @PatientName + '%')
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0;
END
GO

-- 34. Get active admissions
CREATE PROCEDURE [IPU].[uspGetActiveAdmissions]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        pa.PatientAdmissionID,
        pa.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        pa.AdmissionDate,
        pa.AdmissionTime,
        pa.LocalityID,
        l.LocalityTitle as RoomName,
        l.LocalityCode as RoomCode,
        pa.AssignedNurseID,
        pa.AssignedDoctorID,
        pa.AdmissionType,
        pa.Comments,
        pa.IsActive,
        pa.IsParked,
        pa.DischargeID,
        pa.InsertedAt,
        pa.UpdatedAt
    FROM [IPU].[tblPatientAdmission] pa
    INNER JOIN [Profile].[tblPatient] p ON pa.PatientID = p.PatientID
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE pa.IsActive = 1
        AND pa.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0
    ORDER BY pa.AdmissionDate DESC;
END
GO

-- 35. Get patient admission by ID
CREATE PROCEDURE [IPU].[uspGetPatientAdmissionById]
    @AdmissionID INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        pa.PatientAdmissionID,
        pa.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        pa.AdmissionDate,
        pa.AdmissionTime,
        pa.LocalityID,
        l.LocalityTitle as RoomName,
        l.LocalityCode as RoomCode,
        pa.AssignedNurseID,
        pa.AssignedDoctorID,
        pa.AdmissionType,
        pa.Comments,
        pa.IsActive,
        pa.IsParked,
        pa.DischargeID,
        pa.InsertedAt,
        pa.UpdatedAt
    FROM [IPU].[tblPatientAdmission] pa
    INNER JOIN [Profile].[tblPatient] p ON pa.PatientID = p.PatientID
    LEFT JOIN [IPU].[tblLocality] l ON pa.LocalityID = l.LocalityID
    WHERE pa.PatientAdmissionID = @AdmissionID
        AND pa.IsActive = 1
        AND pa.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0;
END
GO

-- 36. Get discharge by ID
CREATE PROCEDURE [IPU].[uspGetDischargeById]
    @DischargeID INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        d.DischargeID,
        d.PatientID,
        p.pFullName as PatientName,
        p.ChartNumber,
        d.DischargeDate,
        d.MedicalDischargeDate,
        d.PhysicalDischargeDate,
        d.DischargeReasonID,
        d.Outcome,
        d.DischargedDestination,
        d.IsParked,
        d.IsActive,
        d.InsertedAt,
        d.UpdatedAt
    FROM [Appointment].[tblDischarge] d
    INNER JOIN [Profile].[tblPatient] p ON d.PatientID = p.PatientID
    WHERE d.DischargeID = @DischargeID
        AND d.IsActive = 1
        AND d.IsDeleted = 0
        AND p.pIsActive = 1
        AND p.pIsDeleted = 0;
END
GO
