using IPU.API.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Add repository services (temporarily commented for debugging)
// builder.Services.AddScoped<ILocalityRepository, LocalityRepository>();
// builder.Services.AddScoped<IPatientRepository, PatientRepository>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "IPU Configuration API",
        Version = "v1",
        Description = "API for managing IPU Buildings, Wards, and Rooms using unified tblLocality table"
    });
});

// Add logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "IPU Configuration API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// Map controllers
app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => new {
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Environment = app.Environment.EnvironmentName,
    Version = "1.0.0"
});

// Test endpoint
app.MapGet("/test", () => new {
    Message = "IPU Configuration API is working!",
    Timestamp = DateTime.UtcNow,
    Status = "OK"
});

app.Run();
