using IPU.API.Models;
using IPU.API.DTOs;

namespace IPU.API.Data
{
    public interface IPatientRepository
    {
        // Patient statistics
        Task<int> GetTotalInpatientsAsync();
        Task<int> GetAdmittedPatientsCountAsync();
        Task<PatientAdmissionStatusDto> CheckPatientAdmissionStatusAsync(string patientName);
        
        // Date-based queries
        Task<List<DischargeDto>> GetDischargedPatientsByDateAsync(DateTime date);
        Task<List<PatientAdmissionDto>> GetAdmittedPatientsByDateAsync(DateTime date);
        Task<List<PatientAdmissionDto>> GetMedicallyDischargedPatientsAsync();
        
        // Room allocation
        Task<List<RoomAllocationDto>> GetTodayAdmittedPatientsRoomAllocationAsync();
        Task<List<AvailableRoomDto>> GetAvailableRoomsAsync();
        Task<bool> UpdatePatientRoomAllocationAsync(int patientAdmissionId, int newRoomId, string? comments = null);
        
        // Patient management
        Task<Patient?> GetPatientByIdAsync(int patientId);
        Task<Patient?> GetPatientByNameAsync(string patientName);
        Task<List<PatientAdmissionDto>> GetActiveAdmissionsAsync();
        Task<PatientAdmissionDto?> GetPatientAdmissionByIdAsync(int admissionId);
        
        // Discharge management
        Task<List<DischargeDto>> GetTodayDischargedPatientsAsync();
        Task<DischargeDto?> GetDischargeByIdAsync(int dischargeId);
    }
}
