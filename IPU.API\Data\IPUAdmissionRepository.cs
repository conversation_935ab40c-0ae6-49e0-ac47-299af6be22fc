using IPU.API.Models;
using Microsoft.Data.SqlClient;
using System.Data;

namespace IPU.API.Data
{
    /// <summary>
    /// Repository implementation for IPU admission operations
    /// </summary>
    public class IPUAdmissionRepository : IIPUAdmissionRepository
    {
        private readonly string _connectionString;

        public IPUAdmissionRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Search for patients by name or NHI number
        /// </summary>
        public async Task<PatientSearchResponse> SearchPatientsAsync(PatientSearchRequest request)
        {
            var response = new PatientSearchResponse();
            
            if (string.IsNullOrWhiteSpace(request.PatientName) && string.IsNullOrWhiteSpace(request.NHINumber))
            {
                response.Message = "Please provide either patient name or NHI number for search.";
                return response;
            }

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspSearchPatients", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@PatientName", (object?)request.PatientName ?? DBNull.Value);
                command.Parameters.AddWithValue("@NHINumber", (object?)request.NHINumber ?? DBNull.Value);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    response.Patients.Add(new PatientInfo
                    {
                        PatientID = reader.GetInt32("PatientID"),
                        FullName = reader.GetString("FullName"),
                        NHINumber = reader.IsDBNull("NHINumber") ? "" : reader.GetString("NHINumber"),
                        DateOfBirth = reader.IsDBNull("DateOfBirth") ? null : reader.GetDateTime("DateOfBirth"),
                        Gender = reader.IsDBNull("Gender") ? "" : reader.GetString("Gender"),
                        IsAdmitted = reader.GetBoolean("IsAdmitted")
                    });
                }

                response.Found = response.Patients.Any();
                response.Message = response.Found 
                    ? $"Found {response.Patients.Count} patient(s)" 
                    : "No patients found matching the search criteria.";
            }
            catch (Exception ex)
            {
                response.Message = $"Error searching patients: {ex.Message}";
            }

            return response;
        }

        /// <summary>
        /// Get patient by exact name match
        /// </summary>
        public async Task<PatientInfo?> GetPatientByNameAsync(string patientName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetPatientByName", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@PatientName", patientName);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new PatientInfo
                    {
                        PatientID = reader.GetInt32("PatientID"),
                        FullName = reader.GetString("FullName"),
                        NHINumber = reader.IsDBNull("NHINumber") ? "" : reader.GetString("NHINumber"),
                        DateOfBirth = reader.IsDBNull("DateOfBirth") ? null : reader.GetDateTime("DateOfBirth"),
                        Gender = reader.IsDBNull("Gender") ? "" : reader.GetString("Gender"),
                        IsAdmitted = reader.GetBoolean("IsAdmitted")
                    };
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return null;
        }

        /// <summary>
        /// Get patient by NHI number
        /// </summary>
        public async Task<PatientInfo?> GetPatientByNHIAsync(string nhiNumber)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetPatientByNHI", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@NHINumber", nhiNumber);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new PatientInfo
                    {
                        PatientID = reader.GetInt32("PatientID"),
                        FullName = reader.GetString("FullName"),
                        NHINumber = reader.IsDBNull("NHINumber") ? "" : reader.GetString("NHINumber"),
                        DateOfBirth = reader.IsDBNull("DateOfBirth") ? null : reader.GetDateTime("DateOfBirth"),
                        Gender = reader.IsDBNull("Gender") ? "" : reader.GetString("Gender"),
                        IsAdmitted = reader.GetBoolean("IsAdmitted")
                    };
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return null;
        }

        /// <summary>
        /// Create a new IPU admission
        /// </summary>
        public async Task<IPUAdmissionResponse> CreateAdmissionAsync(IPUAdmissionRequest request)
        {
            var response = new IPUAdmissionResponse();

            try
            {
                // First, find the patient
                PatientInfo? patient = null;
                
                if (!string.IsNullOrWhiteSpace(request.PatientName))
                {
                    patient = await GetPatientByNameAsync(request.PatientName);
                }
                else if (!string.IsNullOrWhiteSpace(request.NHINumber))
                {
                    patient = await GetPatientByNHIAsync(request.NHINumber);
                }

                if (patient == null)
                {
                    response.Message = "Patient not found. Please verify the patient name or NHI number.";
                    return response;
                }

                if (patient.IsAdmitted)
                {
                    response.Message = $"Patient {patient.FullName} is already admitted to IPU.";
                    return response;
                }

                // Create admission using stored procedure
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspIPUFormInsertUpdate", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // Add all parameters for the stored procedure
                command.Parameters.AddWithValue("@pPatientID", patient.PatientID);
                command.Parameters.AddWithValue("@pAdmissionDate", request.AdmissionDate);
                command.Parameters.AddWithValue("@pAdmissionTime", DateTime.Today.Add(request.AdmissionTime));
                command.Parameters.AddWithValue("@pReasonID", request.ReasonID);
                command.Parameters.AddWithValue("@pAssignedDoctorID", request.AssignedDoctorID);
                command.Parameters.AddWithValue("@pAdmissionSubStatusID", request.AdmissionSubStatusID);
                command.Parameters.AddWithValue("@pSpecialityID", request.SpecialityID);
                command.Parameters.AddWithValue("@pAdmissionType", request.AdmissionType);
                command.Parameters.AddWithValue("@pLocationID", request.LocationID);
                command.Parameters.AddWithValue("@pPatientManagement", request.PatientManagement);
                command.Parameters.AddWithValue("@pRoomID", request.RoomID);
                command.Parameters.AddWithValue("@pACC45ID", (object?)request.ACC45ID ?? DBNull.Value);
                command.Parameters.AddWithValue("@pTriageID", request.TriageID);
                command.Parameters.AddWithValue("@pAlcohalStatus", request.AlcoholInvolved);
                command.Parameters.AddWithValue("@pSeendate", (object?)request.TreatmentStartedDate ?? DBNull.Value);
                command.Parameters.AddWithValue("@pTimeSeen", request.TreatmentStartedTime.HasValue ? DateTime.Today.Add(request.TreatmentStartedTime.Value) : DBNull.Value);
                command.Parameters.AddWithValue("@pIPUAriivalMethodID", request.IPUArrivalMethodID);
                command.Parameters.AddWithValue("@pSeenByDoctor", (object?)request.SeenByDoctorID ?? DBNull.Value);
                command.Parameters.AddWithValue("@pComments", (object?)request.Comments ?? DBNull.Value);
                command.Parameters.AddWithValue("@pcomplaint", (object?)request.Complaint ?? DBNull.Value);
                command.Parameters.AddWithValue("@pFamilyViolenceScreened", request.FamilyViolenceScreened);
                command.Parameters.AddWithValue("@pFVOutcome", request.FVOutcome);
                command.Parameters.AddWithValue("@pReasonForNotScreeningID", (object?)request.ReasonForNotScreeningID ?? DBNull.Value);
                command.Parameters.AddWithValue("@pVisitors", request.VisitorFlag);
                command.Parameters.AddWithValue("@pReligiousVisitors", request.ReligiousVisitors);
                command.Parameters.AddWithValue("@pPrivacyStatementOne", request.PrivacyStatementOne);
                command.Parameters.AddWithValue("@pPrivacyStatementTwo", request.PrivacyStatementTwo);
                command.Parameters.AddWithValue("@pPracticeID", request.PracticeID);
                command.Parameters.AddWithValue("@pUserLoggingID", request.UserLoggingID);
                command.Parameters.AddWithValue("@pInsertedBy", request.InsertedBy);

                // Output parameter
                var outputParam = new SqlParameter("@OutputParam", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();

                var patientAdmissionId = (int)outputParam.Value;

                if (patientAdmissionId > 0)
                {
                    response.Success = true;
                    response.PatientAdmissionID = patientAdmissionId;
                    response.Patient = patient;
                    response.Message = $"Patient {patient.FullName} has been successfully admitted to IPU.";
                }
                else if (patientAdmissionId == -2)
                {
                    response.Message = "Patient is already admitted to IPU.";
                }
                else
                {
                    response.Message = "Failed to create admission. Please try again.";
                }
            }
            catch (Exception ex)
            {
                response.Message = $"Error creating admission: {ex.Message}";
            }

            return response;
        }

        /// <summary>
        /// Get all dropdown data needed for IPU admission form
        /// </summary>
        public async Task<IPUAdmissionDropdownData> GetAdmissionDropdownDataAsync()
        {
            var data = new IPUAdmissionDropdownData();

            try
            {
                // Get all dropdown data in parallel for better performance
                var admissionReasonsTask = GetAdmissionReasonsAsync();
                var doctorsTask = GetDoctorsAsync();
                var admissionSourcesTask = GetAdmissionSourcesAsync();
                var specialitiesTask = GetSpecialitiesAsync();
                var locationsTask = GetLocationsAsync();
                var roomsTask = GetAvailableRoomsAsync();
                var triageScoresTask = GetTriageScoresAsync();
                var arrivalMethodsTask = GetArrivalMethodsAsync();
                var familyViolenceReasonsTask = GetFamilyViolenceReasonsAsync();

                await Task.WhenAll(
                    admissionReasonsTask,
                    doctorsTask,
                    admissionSourcesTask,
                    specialitiesTask,
                    locationsTask,
                    roomsTask,
                    triageScoresTask,
                    arrivalMethodsTask,
                    familyViolenceReasonsTask
                );

                data.AdmissionReasons = await admissionReasonsTask;
                data.Doctors = await doctorsTask;
                data.AdmissionSources = await admissionSourcesTask;
                data.Specialities = await specialitiesTask;
                data.Locations = await locationsTask;
                data.AvailableRooms = await roomsTask;
                data.TriageScores = await triageScoresTask;
                data.ArrivalMethods = await arrivalMethodsTask;
                data.FamilyViolenceReasons = await familyViolenceReasonsTask;
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return data;
        }

        /// <summary>
        /// Get admission reasons
        /// </summary>
        public async Task<List<IPUEnumeration>> GetAdmissionReasonsAsync()
        {
            var reasons = new List<IPUEnumeration>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[POC].[uspGetIPUEnumerationByID]", connection) //admission reason
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    reasons.Add(new IPUEnumeration
                    {
                        IPUEnumerationID = reader.GetInt32("IPUEnumerationID"),
                        Description = reader.GetString("Description"),
                        ParentID = reader.GetInt32("ParentID")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return reasons;
        }

        /// <summary>
        /// Get available doctors/providers
        /// </summary>
        public async Task<List<Provider>> GetDoctorsAsync()
        {
            var doctors = new List<Provider>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[Profile].[uspProviderPracticeGet]", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
               command.Parameters.AddWithValue("@PatientID", 128);
                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    doctors.Add(new Provider
                    {
                        ProviderUUID = reader.GetString("ProviderUUID"),
                        FirstName = reader.GetString("FirstName"),
                        LastName = reader.GetString("LastName"),
                        FullName = reader.GetString("FullName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return doctors;
        }

        /// <summary>
        /// Get admission sources
        /// </summary>
        public async Task<List<AdmissionSubStatus>> GetAdmissionSourcesAsync()
        {
            var sources = new List<AdmissionSubStatus>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("Lookup.uspIPUAdmissionSubStatusGet", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
             command.Parameters.AddWithValue("@pReferralStatusID", 0);
            command.Parameters.AddWithValue("pPracticeID", 128);
                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    sources.Add(new AdmissionSubStatus
                    {
                        AdmissionSubStatusID = reader.GetInt32("AdmissionSubStatusID"),
                        AdmissionSubStatusName = reader.GetString("AdmissionSubStatusName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return sources;
        }

        /// <summary>
        /// Get specialities
        /// </summary>
        public async Task<List<ReferralSpeciality>> GetSpecialitiesAsync()
        {
            var specialities = new List<ReferralSpeciality>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetSpecialities", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    specialities.Add(new ReferralSpeciality
                    {
                        SpecialityID = reader.GetInt32("SpecialityID"),
                        SpecialityName = reader.GetString("SpecialityName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return specialities;
        }

        /// <summary>
        /// Get practice locations
        /// </summary>
        public async Task<List<PracticeLocation>> GetLocationsAsync()
        {
            var locations = new List<PracticeLocation>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetLocations", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    locations.Add(new PracticeLocation
                    {
                        PracticeLocationID = reader.GetInt32("PracticeLocationID"),
                        LocationName = reader.GetString("LocationName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return locations;
        }

        /// <summary>
        /// Get available rooms for a specific location
        /// </summary>
        public async Task<List<DropdownItem>> GetAvailableRoomsAsync(int? locationId = null)
        {
            var rooms = new List<DropdownItem>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetAvailableRooms", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@LocationID", (object?)locationId ?? DBNull.Value);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    rooms.Add(new DropdownItem
                    {
                        ID = reader.GetInt32("LocalityID"),
                        Name = reader.GetString("LocalityTitle"),
                        Description = reader.IsDBNull("Description") ? null : reader.GetString("Description")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return rooms;
        }

        /// <summary>
        /// Get triage scores
        /// </summary>
        public async Task<List<TriageScore>> GetTriageScoresAsync()
        {
            var scores = new List<TriageScore>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("IPU.uspGetTriageScores-MCP", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    scores.Add(new TriageScore
                    {
                        TriageScoreID = reader.GetInt32("TriageScoreID"),
                        TriageScoreName = reader.GetString("TriageScoreName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return scores;
        }

        /// <summary>
        /// Get IPU arrival methods
        /// </summary>
        public async Task<List<IpuArrivalMethod>> GetArrivalMethodsAsync()
        {
            var methods = new List<IpuArrivalMethod>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("Lookup.uspGetIPUArrivalMethod", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    methods.Add(new IpuArrivalMethod
                    {
                        IPUArrivalMethodID = reader.GetInt32("IPUArrivalMethodID"),
                        ArrivalMethod = reader.GetString("ArrivalMethod")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return methods;
        }

        /// <summary>
        /// Get family violence screening reasons
        /// </summary>
        public async Task<List<FamilyViolenceReason>> GetFamilyViolenceReasonsAsync()
        {
            var reasons = new List<FamilyViolenceReason>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("lookup.uspReasonForNotScreening", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    reasons.Add(new FamilyViolenceReason
                    {
                        ReasonForNotScreeningID = reader.GetInt32("ReasonForNotScreeningID"),
                        ReasonForNotScreeningName = reader.GetString("ReasonForNotScreeningName")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return reasons;
        }

        /// <summary>
        /// Get ACC45 numbers for a patient
        /// </summary>
        public async Task<List<ACC45>> GetACC45NumbersAsync(int patientId)
        {
            var acc45Numbers = new List<ACC45>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[Appointment].[uspGetACC45ByPatientwithDiagnosis]", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@pPatientID", patientId);
                command.Parameters.AddWithValue("@pPracticeID", 128 );
                command.Parameters.AddWithValue("@pPracticeLocationID", 0);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    acc45Numbers.Add(new ACC45
                    {
                        UUID = reader.GetString("UUID"),
                        ACC45Number = reader.GetString("ACC45Number")
                    });
                }
            }
            catch (Exception)
            {
                // Log exception in real implementation
            }

            return acc45Numbers;
        }
    }
}
