namespace IPU.API.Models
{
    /// <summary>
    /// Represents a profile from [Profile].[tblProfile] table
    /// </summary>
    public class Profile
    {
        public int ProfileID { get; set; }
        public byte ProfileTypeID { get; set; }
        public string? NHINumber { get; set; }
        public bool IsNHIValidate { get; set; }
        public int? TitleID { get; set; }
        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? FamilyName { get; set; }
        public string? FullName { get; set; }
        public string? PreferredName { get; set; }
        public string? OtherMaidenName { get; set; }
        public int? MaritalStatusID { get; set; }
        public int? GenderID { get; set; }
        public DateTime? DOB { get; set; }
        public string? AGE { get; set; }
        public bool IsAlive { get; set; }
        public DateTime? DeathDate { get; set; }
        public int? DOBSourceID { get; set; }
        public string? PicturePath { get; set; }
        public bool ConsentedtoShare { get; set; }
        public string? PlaceofBirth { get; set; }
        public int? CountryofBirthID { get; set; }
        public string? CellNumber { get; set; }
        public string? DayPhone { get; set; }
        public string? NightPhone { get; set; }
        public string? Email { get; set; }
        public string? SecondaryEmail { get; set; }
        public int? PreferredContactMethodID { get; set; }
        public bool ConsentTextMessaging { get; set; }
        public bool IsPortalUser { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public int InsertedBy { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime InsertedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? MedTechID { get; set; }
        public bool IsSameAddress { get; set; }
        public string? Notes { get; set; }
        public bool IsGenderSelfIdentified { get; set; }
        public int? SexID { get; set; }
        public bool IsWorkedVisaRequired { get; set; }
        public DateTime? WorkVisaExpiry { get; set; }
        public long? UserLoggingID { get; set; }
        public decimal? Balance { get; set; }
        public decimal? MedTechBalance { get; set; }
        public DateTime? MedTechBalanceDate { get; set; }
        public DateTime? MedTechDATELASTPAY { get; set; }
        public DateTime? MedTechDATELASTSTMT { get; set; }
        public byte? RegisteredStatusID { get; set; }
        public string? MedTechNOK { get; set; }
        public int? PracticeID { get; set; }
        public decimal CalculatedBalance { get; set; }
        public DateTime? LastStatementDate { get; set; }
        public DateTime? LastInvoiceDate { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public int? MasterProfileID { get; set; }
        public decimal? CalculatedBalanceEOM { get; set; }
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
        public string? NHIVersion { get; set; }
        public int? NHIStatusID { get; set; }
        public DateTime? NHIVersionDate { get; set; }
        public bool IsGP2GP { get; set; }
        public string? ResidencePhoneNo { get; set; }
        public byte? DataSourceID { get; set; }
        public bool? ShowFullNameOnPortal { get; set; }
        public bool IsIncludeStatementFee { get; set; }
        public bool IsIncludeAccountFee { get; set; }
        public bool IsTestRecord { get; set; }
        public int? BloodGroupID { get; set; }
        public bool SendQEDtoNES { get; set; }
        public DateTime? SendQEDtoNESInvDate { get; set; }
        public string? Gmail { get; set; }
        public bool? IsAdvanceCarePlanEnabled { get; set; }
        public int? PracticeLocationId { get; set; }
        public string? ADUserName { get; set; }
        public bool IsEnabledLabRadTask { get; set; }
        public int? MergedProfileID { get; set; }
        public DateTime? LastStatementSentToPortalDate { get; set; }
        public byte? LastStatementSendingSourceID { get; set; }
        public long? PortalMessageID { get; set; }
        public bool ConsentAutoReminderTextMessaging { get; set; }
        public Guid? DocumentKey { get; set; }
        public string? FirstNameAlpha { get; set; }
        public string? FirstNameNumeric { get; set; }
        public string? FamilyNameAlpha { get; set; }
        public string? FamilyNameNumeric { get; set; }
        public string? MiddleNameAlpha { get; set; }
        public string? MiddleNameNumeric { get; set; }
        public string? PreferredNameAlpha { get; set; }
        public string? PreferredNameNumeric { get; set; }
        public bool IsArchived { get; set; }
        public bool? IsNOKDeclined { get; set; }
        public bool IsSynctoExternalFinance { get; set; }
        public string? ExternalFinanceReferenceNo { get; set; }
        public int? CountryofBirthSourceID { get; set; }
        public int? PodID { get; set; }
        public DateTime? ExternalFinanceSyncDate { get; set; }
        public string? ExternalFinanceSyncFailedReason { get; set; }
        public DateTime? LastStatementSentDate { get; set; }
        public bool IsCommonService { get; set; }
        public string? SurthrenCrossUniqueKey { get; set; }
        public bool? IsConsultOpenOnSingleRecordTypeId { get; set; }
        public bool? IsConsultOpenOnSingleRecord { get; set; }
        public Guid UUID { get; set; }
        public string? WebsiteURL { get; set; }
        public decimal? MedTechAdjBalance { get; set; }
        public decimal CalculatedBalanceOtherThanAccountHolder { get; set; }
        public bool? IsAddedFromSinglePage { get; set; }
        public string? OwnerIdentityForCellPhoneNumber { get; set; }
        public string? OwnerIdentityForDayPhone { get; set; }
        public string? OwnerIdentityForNightPhone { get; set; }
        public string? OwnerIdentityForEmail { get; set; }
        public bool? UnAuthorizeInbox { get; set; }
        public string? NHINumberBlankReason { get; set; }
        public int? MasterID { get; set; }
        public bool? IsSyncCalendar { get; set; }
        public int? PKID_ReplaceWith { get; set; }
        public string? NameType { get; set; }
        public DateTime? EffectivePeriod { get; set; }
        public DateTime? EffectivePeriodTo { get; set; }
        public int? CitizenshipID { get; set; }
        public int? NZCitizenshipID { get; set; }
        public int IsMetaDataSyncedFromWindowService { get; set; }
        public int? NameSourceID { get; set; }
        public int IsAddressMetaDataSyncedFromWindowService { get; set; }
        public int? NHIBlankReasonID { get; set; }
        public string? NHISyncSkipReason { get; set; }
        public string? LastSelectedSmartPayDevice { get; set; }
        public string? LastSelectedVerifoneDevice { get; set; }
        public string? ExternalID { get; set; }
        public bool? ISAvailableOnlineBooking { get; set; }
        public bool? NewInvoiceControl { get; set; }
        public bool? IsSpecialPurposeUser { get; set; }
        public bool IsMedRxAPIUser { get; set; }
        public int? WorldTimezoneId { get; set; }
        public bool? IsPhoneNoValidated { get; set; }
        public bool? IsEmailValidated { get; set; }
        public byte? BirthOrderTypeID { get; set; }
        public byte? BirthPluralityId { get; set; }
        public byte? DateofBirthAccuracyID { get; set; }
        public byte? DDAccuracyID { get; set; }
        public byte? IndividualNameUsageTypeID { get; set; }
        public byte? NameConditionalUseID { get; set; }
        public int? DODSourceID { get; set; }
        public int? IHIStatusID { get; set; }
        public string? DvaNo { get; set; }
        public string? MedicardNo { get; set; }
        public bool? IHIConflict { get; set; }
        public string? OtherSpiritualBelief { get; set; }

        // Navigation properties
        public List<Patient> Patients { get; set; } = new List<Patient>();
    }
}
