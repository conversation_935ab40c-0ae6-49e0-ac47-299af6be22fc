-- =============================================
-- IPU Admission API - Required Stored Procedures
-- =============================================

USE [PMS_NZ_LOCAL_NZTFS]
GO

-- =============================================
-- Patient Search Procedures
-- =============================================

-- Search patients by name or NHI
CREATE OR ALTER PROCEDURE [IPU].[uspSearchPatients-MCP]
    @PatientName NVARCHAR(255) = NULL,
    @NHINumber NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        p.PatientID,
        CONCAT(p.FirstName, ' ', p.LastName) AS FullName,
        p.NHINumber,
        p.DateOfBirth,
        p.Gender,
        CASE WHEN pa.PatientID IS NOT NULL THEN 1 ELSE 0 END AS IsAdmitted
    FROM tblpatient p
    LEFT JOIN tblpatientadmission pa ON p.PatientID = pa.PatientID 
        AND pa.DischargeDate IS NULL
        AND pa.IsDeleted = 0
    WHERE 
        (@PatientName IS NULL OR CONCAT(p.FirstName, ' ', p.LastName) LIKE '%' + @PatientName + '%')
        AND (@NHINumber IS NULL OR p.NHINumber = @NHINumber)
        AND p.IsDeleted = 0
    ORDER BY p.FirstName, p.LastName;
END
GO

-- Get patient by exact name match
CREATE OR ALTER PROCEDURE [IPU].[uspGetPatientByName-MCP]
    @PatientName NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        p.PatientID,
        CONCAT(p.FirstName, ' ', p.LastName) AS FullName,
        p.NHINumber,
        p.DateOfBirth,
        p.Gender,
        CASE WHEN pa.PatientID IS NOT NULL THEN 1 ELSE 0 END AS IsAdmitted
    FROM tblpatient p
    LEFT JOIN tblpatientadmission pa ON p.PatientID = pa.PatientID 
        AND pa.DischargeDate IS NULL
        AND pa.IsDeleted = 0
    WHERE 
        CONCAT(p.FirstName, ' ', p.LastName) = @PatientName
        AND p.IsDeleted = 0;
END
GO

-- Get patient by NHI number
CREATE OR ALTER PROCEDURE [IPU].[uspGetPatientByNHI-MCP]
    @NHINumber NVARCHAR(20)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        p.PatientID,
        CONCAT(p.FirstName, ' ', p.LastName) AS FullName,
        p.NHINumber,
        p.DateOfBirth,
        p.Gender,
        CASE WHEN pa.PatientID IS NOT NULL THEN 1 ELSE 0 END AS IsAdmitted
    FROM tblpatient p
    LEFT JOIN tblpatientadmission pa ON p.PatientID = pa.PatientID 
        AND pa.DischargeDate IS NULL
        AND pa.IsDeleted = 0
    WHERE 
        p.NHINumber = @NHINumber
        AND p.IsDeleted = 0;
END
GO

-- =============================================
-- Dropdown Data Procedures
-- =============================================

-- Get admission reasons

-- Get doctors/providers


-- Get admission sources


-- Get specialities
CREATE OR ALTER PROCEDURE [IPU].[uspGetSpecialities-MCP]
AS
BEGIN
    SET NOCOUNT ON;
    
   select IPUSpecialityID as SpecialityID,
					       IPUSpeciality as SpecialityName
						   from lookup.tblSpecialityIPU 
						   where isactive =1 and isdeleted =0
END
GO

-- Get practice locations
CREATE OR ALTER PROCEDURE [IPU].[uspGetLocations]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        PracticeLocationID,
        LocationName
    FROM tblPracticeLocation
    WHERE IsActive = 1
        AND IsDeleted = 0
    ORDER BY LocationName;
END
GO

-- Get available rooms
CREATE OR ALTER PROCEDURE [IPU].[uspGetAvailableRooms]
    @LocationID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        l.LocalityID,
        l.LocalityTitle,
        l.LocalityDescription AS Description
    FROM IPU.tblLocality l
    WHERE l.LocalityTypeID = 3 -- Assuming 3 is for rooms
        AND l.IsActive = 1
        AND l.IsDeleted = 0
        AND (@LocationID IS NULL OR l.ParentLocalityID = @LocationID)
    ORDER BY l.LocalityTitle;
END
GO

-- Get triage scores
CREATE OR ALTER PROCEDURE [IPU].[uspGetTriageScores-MCP]
AS
BEGIN
    SET NOCOUNT ON;
    
			select EnumerationID as TriageScoreID, 
	       EnumerationType as TriageScoreName 
		   from lookup.tblEnumeration (nolock) where ParentID = 398
			
END
GO  



PRINT 'IPU Admission stored procedures created successfully!'
