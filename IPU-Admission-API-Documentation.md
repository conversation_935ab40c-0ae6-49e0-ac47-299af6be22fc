# IPU Admission API Documentation

## Overview
This API provides comprehensive endpoints for IPU (Inpatient Unit) admission management, specifically designed for MCP (Model Context Protocol) server integration. The API supports natural language processing for patient admission requests.

## Base URL
```
http://localhost:5077/api/ipuadmission
```

## Key Features
- **MCP-Friendly Design**: Supports natural language queries like "admit patient <PERSON>" or "admit patient with NHI ABC1234"
- **Patient Verification**: Search and verify patients before admission
- **Comprehensive Form Data**: All dropdown data for the IPU admission form
- **Flexible Patient Identification**: Supports both patient name and NHI number

## API Endpoints

### 1. Patient Search & Verification

#### Search Patients
**POST** `/search-patients`

Search for patients by name or NHI number for verification before admission.

**Request Body:**
```json
{
  "patientName": "<PERSON>",
  "nhiNumber": "ABC1234"
}
```

**Response:**
```json
{
  "found": true,
  "message": "Found 1 patient(s)",
  "patients": [
    {
      "patientID": 123,
      "fullName": "<PERSON>",
      "nhiNumber": "ABC1234",
      "dateOfBirth": "1980-05-15T00:00:00",
      "gender": "Male",
      "isAdmitted": false
    }
  ]
}
```

#### Get Patient by Name
**GET** `/patient/by-name/{patientName}`

Get specific patient information by exact name match.

#### Get Patient by NHI
**GET** `/patient/by-nhi/{nhiNumber}`

Get specific patient information by NHI number.

### 2. IPU Admission

#### Create Admission
**POST** `/admit-patient`

Create a new IPU admission for a patient.

**Request Body:**
```json
{
  "patientName": "John Smith",
  "nhiNumber": null,
  "admissionDate": "2025-01-23T00:00:00",
  "admissionTime": "14:30:00",
  "reasonID": 1,
  "assignedDoctorID": "DOC001",
  "admissionSubStatusID": 1,
  "specialityID": 1,
  "admissionType": "Acute",
  "locationID": 1,
  "patientManagement": "Inpatient",
  "roomID": 101,
  "acc45ID": null,
  "triageID": 1,
  "alcoholInvolved": "No Alcohol Involved",
  "treatmentStartedDate": null,
  "treatmentStartedTime": null,
  "ipuArrivalMethodID": 1,
  "seenByDoctorID": null,
  "comments": "Patient admitted for observation",
  "complaint": "Chest pain",
  "familyViolenceScreened": "Yes",
  "fvOutcome": "Negative",
  "reasonForNotScreeningID": null,
  "visitorFlag": false,
  "religiousVisitors": false,
  "privacyStatementOne": true,
  "privacyStatementTwo": true,
  "practiceID": 1,
  "userLoggingID": 1,
  "insertedBy": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Patient John Smith has been successfully admitted to IPU.",
  "patientAdmissionID": 456,
  "patient": {
    "patientID": 123,
    "fullName": "John Smith",
    "nhiNumber": "ABC1234",
    "dateOfBirth": "1980-05-15T00:00:00",
    "gender": "Male",
    "isAdmitted": true
  }
}
```

#### MCP Natural Language Admission
**POST** `/admit-by-query?query={naturalLanguageQuery}`

Process natural language admission requests designed for MCP integration.

**Examples:**
- `?query=admit patient John Smith`
- `?query=admit patient with NHI ABC1234`
- `?query=please admit the patient John`

### 3. Dropdown Data Endpoints

#### Get All Dropdown Data
**GET** `/dropdown-data`

Returns comprehensive dropdown data for the entire IPU admission form.

#### Individual Dropdown Endpoints
- **GET** `/dropdown/admission-reasons` - Get admission reasons
- **GET** `/dropdown/doctors` - Get available doctors
- **GET** `/dropdown/admission-sources` - Get admission sources
- **GET** `/dropdown/specialities` - Get medical specialities
- **GET** `/dropdown/locations` - Get practice locations
- **GET** `/dropdown/rooms?locationId={id}` - Get available rooms (optionally filtered by location)
- **GET** `/dropdown/triage-scores` - Get triage scores
- **GET** `/dropdown/arrival-methods` - Get IPU arrival methods
- **GET** `/dropdown/family-violence-reasons` - Get family violence screening reasons
- **GET** `/dropdown/acc45-numbers/{patientId}` - Get ACC45 numbers for a patient

## MCP Integration Examples

### Example 1: Patient Verification
```
User: "Is patient John Smith available for admission?"
MCP: Calls GET /patient/by-name/John Smith
```

### Example 2: Natural Language Admission
```
User: "Please admit patient John Smith to IPU"
MCP: Calls POST /admit-by-query?query=admit patient John Smith
```

### Example 3: NHI-based Admission
```
User: "Admit patient with NHI ABC1234"
MCP: Calls POST /admit-by-query?query=admit patient with NHI ABC1234
```

## Required Database Setup

Before using the API, execute the following SQL script:
```sql
-- File: StoredProcedures/IPU-Admission-Stored-Procedures.sql
-- This creates all required stored procedures for the IPU admission functionality
```

## Error Handling

The API returns appropriate HTTP status codes:
- **200**: Success
- **400**: Bad Request (invalid data)
- **404**: Patient not found
- **409**: Conflict (patient already admitted)
- **500**: Internal server error

## Data Validation

### Required Fields for Admission:
- Patient identification (either `patientName` or `nhiNumber`)
- `admissionDate` and `admissionTime`
- `reasonID`, `assignedDoctorID`, `admissionSubStatusID`
- `specialityID`, `admissionType`, `locationID`
- `patientManagement`, `roomID`, `triageID`
- `alcoholInvolved`, `familyViolenceScreened`, `fvOutcome`

### Admission Types:
- "Acute"
- "Arranged"

### Patient Management Types:
- "Inpatient"
- "Day Case"
- "Rest Home"
- "ED"

### Alcohol Involvement Options:
- "No Alcohol Involved"
- "Unknown"
- "Yes Alcohol Involved"
- "Secondary (Others consumed alcohol)"

### Family Violence Outcomes:
- "Positive"
- "Negative"
- "Historical"

## Security Considerations

- All endpoints validate input data
- Patient information is protected
- Database connections use parameterized queries to prevent SQL injection
- Error messages don't expose sensitive system information

## Testing

Use the Swagger UI at `http://localhost:5077/swagger` to test all endpoints interactively.

## Support

For issues or questions about the IPU Admission API, refer to the API logs or contact the development team.
