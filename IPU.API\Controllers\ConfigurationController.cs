using IPU.API.Data;
using IPU.API.DTOs;
using IPU.API.Models;
using Microsoft.AspNetCore.Mvc;

namespace IPU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class ConfigurationController : ControllerBase
    {
        private readonly ILocalityRepository _localityRepository;
        private readonly ILogger<ConfigurationController> _logger;

        public ConfigurationController(ILocalityRepository localityRepository, ILogger<ConfigurationController> logger)
        {
            _localityRepository = localityRepository;
            _logger = logger;
        }

        #region Buildings (LocalityTypeID = 1)

        /// <summary>
        /// Get all buildings with count of wards
        /// </summary>
        [HttpGet("buildings")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetBuildings()
        {
            try
            {
                var buildings = await _localityRepository.GetAllLocalitiesByTypeAsync(LocalityTypes.Building);
                var buildingDtos = new List<LocalityDto>();

                foreach (var building in buildings)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(building.LocalityID);
                    buildingDtos.Add(MapToLocalityDto(building, childCount));
                }

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(buildingDtos, "Buildings retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving buildings");
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get building by ID
        /// </summary>
        [HttpGet("buildings/{id}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> GetBuilding(int id)
        {
            try
            {
                var building = await _localityRepository.GetLocalityByIdAsync(id);
                if (building == null || building.LocalityTypeID != LocalityTypes.Building)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Building not found"));
                }

                var childCount = await _localityRepository.GetChildCountAsync(building.LocalityID);
                var buildingDto = MapToLocalityDto(building, childCount);

                return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, "Building retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving building with ID {BuildingId}", id);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Create new building
        /// </summary>
        [HttpPost("buildings")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> CreateBuilding([FromBody] CreateLocalityDto createDto)
        {
            try
            {
                // Validate that it's a building
                if (createDto.LocalityTypeID != LocalityTypes.Building)
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("LocalityTypeID must be 1 for buildings"));
                }

                // Check if code already exists
                if (await _localityRepository.LocalityCodeExistsAsync(createDto.LocalityCode))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Building code already exists"));
                }

                var locality = MapToLocality(createDto);
                locality.LocalityTypeID = LocalityTypes.Building;
                locality.InsertedBy = 1; // TODO: Get from current user
                locality.UpdatedBy = 1;
                locality.UserLoggingID = 1;
                locality.InsertedAt = DateTime.Now;
                locality.UpdatedAt = DateTime.Now;
                locality.IsActive = true;
                locality.IsDeleted = false;
                locality.IsOccupied = false;

                var newId = await _localityRepository.CreateLocalityAsync(locality);
                var createdBuilding = await _localityRepository.GetLocalityByIdAsync(newId);
                
                if (createdBuilding != null)
                {
                    var buildingDto = MapToLocalityDto(createdBuilding, 0);
                    return CreatedAtAction(nameof(GetBuilding), new { id = newId }, 
                        ApiResponse<LocalityDto>.SuccessResponse(buildingDto, "Building created successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to create building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating building");
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update building
        /// </summary>
        [HttpPut("buildings/{id}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuilding(int id, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                var existingBuilding = await _localityRepository.GetLocalityByIdAsync(id);
                if (existingBuilding == null || existingBuilding.LocalityTypeID != LocalityTypes.Building)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Building not found"));
                }

                // Check if code already exists (excluding current building)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, id))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Building code already exists"));
                }

                // Update the building
                existingBuilding.LocalityTitle = updateDto.LocalityTitle;
                existingBuilding.LocalityCode = updateDto.LocalityCode;
                existingBuilding.LocalityDescription = updateDto.LocalityDescription;
                existingBuilding.Facilities = updateDto.Facilities;
                existingBuilding.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingBuilding.IsHomeLeave = updateDto.IsHomeLeave;
                existingBuilding.SequenceNo = updateDto.SequenceNo;
                existingBuilding.IsActive = updateDto.IsActive;
                existingBuilding.UpdatedBy = 1; // TODO: Get from current user
                existingBuilding.UpdatedAt = DateTime.Now;

                var success = await _localityRepository.UpdateLocalityAsync(existingBuilding);
                if (success)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(existingBuilding.LocalityID);
                    var buildingDto = MapToLocalityDto(existingBuilding, childCount);
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, "Building updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building with ID {BuildingId}", id);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Delete building
        /// </summary>
        [HttpDelete("buildings/{id}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteBuilding(int id)
        {
            try
            {
                var building = await _localityRepository.GetLocalityByIdAsync(id);
                if (building == null || building.LocalityTypeID != LocalityTypes.Building)
                {
                    return NotFound(ApiResponse<bool>.ErrorResponse("Building not found"));
                }

                // Check if building has wards
                var childCount = await _localityRepository.GetChildCountAsync(id);
                if (childCount > 0)
                {
                    return BadRequest(ApiResponse<bool>.ErrorResponse("Cannot delete building that contains wards. Please remove all wards first."));
                }

                var success = await _localityRepository.DeleteLocalityAsync(id);
                if (success)
                {
                    return Ok(ApiResponse<bool>.SuccessResponse(true, "Building deleted successfully"));
                }

                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Failed to delete building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting building with ID {BuildingId}", id);
                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Wards (LocalityTypeID = 2)

        /// <summary>
        /// Get all wards with count of rooms
        /// </summary>
        [HttpGet("wards")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetWards()
        {
            try
            {
                var wards = await _localityRepository.GetAllLocalitiesByTypeAsync(LocalityTypes.Ward);
                var wardDtos = new List<LocalityDto>();

                foreach (var ward in wards)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(ward.LocalityID);
                    wardDtos.Add(MapToLocalityDto(ward, childCount));
                }

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(wardDtos, "Wards retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wards");
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get wards by building ID
        /// </summary>
        [HttpGet("buildings/{buildingId}/wards")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetWardsByBuilding(int buildingId)
        {
            try
            {
                var wards = await _localityRepository.GetChildLocalitiesAsync(buildingId);
                var wardDtos = new List<LocalityDto>();

                foreach (var ward in wards.Where(w => w.LocalityTypeID == LocalityTypes.Ward))
                {
                    var childCount = await _localityRepository.GetChildCountAsync(ward.LocalityID);
                    wardDtos.Add(MapToLocalityDto(ward, childCount));
                }

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(wardDtos, "Wards retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wards for building {BuildingId}", buildingId);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Rooms (LocalityTypeID = 3)

        /// <summary>
        /// Get all rooms
        /// </summary>
        [HttpGet("rooms")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetRooms()
        {
            try
            {
                var rooms = await _localityRepository.GetAllLocalitiesByTypeAsync(LocalityTypes.Room);
                var roomDtos = rooms.Select(room => MapToLocalityDto(room, 0)).ToList();

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(roomDtos, "Rooms retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving rooms");
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get available rooms (not occupied)
        /// </summary>
        [HttpGet("rooms/available")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetAvailableRooms()
        {
            try
            {
                var rooms = await _localityRepository.GetAvailableRoomsAsync();
                var roomDtos = rooms.Select(room => MapToLocalityDto(room, 0)).ToList();

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(roomDtos, "Available rooms retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving available rooms");
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get rooms by ward ID
        /// </summary>
        [HttpGet("wards/{wardId}/rooms")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> GetRoomsByWard(int wardId)
        {
            try
            {
                var rooms = await _localityRepository.GetChildLocalitiesAsync(wardId);
                var roomDtos = rooms.Where(r => r.LocalityTypeID == LocalityTypes.Room)
                                  .Select(room => MapToLocalityDto(room, 0)).ToList();

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(roomDtos, "Rooms retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving rooms for ward {WardId}", wardId);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Search Operations

        /// <summary>
        /// Search buildings by name
        /// </summary>
        [HttpGet("buildings/search")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> SearchBuildings([FromQuery] string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(ApiResponse<List<LocalityDto>>.ErrorResponse("Search term is required"));
                }

                var buildings = await _localityRepository.SearchBuildingsByNameAsync(searchTerm);
                var buildingDtos = new List<LocalityDto>();

                foreach (var building in buildings)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(building.LocalityID);
                    buildingDtos.Add(MapToLocalityDto(building, childCount));
                }

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(buildingDtos,
                    $"Found {buildingDtos.Count} buildings matching '{searchTerm}'"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching buildings with term {SearchTerm}", searchTerm);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Search wards by name
        /// </summary>
        [HttpGet("wards/search")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> SearchWards([FromQuery] string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(ApiResponse<List<LocalityDto>>.ErrorResponse("Search term is required"));
                }

                var wards = await _localityRepository.SearchWardsByNameAsync(searchTerm);
                var wardDtos = new List<LocalityDto>();

                foreach (var ward in wards)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(ward.LocalityID);
                    wardDtos.Add(MapToLocalityDto(ward, childCount));
                }

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(wardDtos,
                    $"Found {wardDtos.Count} wards matching '{searchTerm}'"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching wards with term {SearchTerm}", searchTerm);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Search rooms by name
        /// </summary>
        [HttpGet("rooms/search")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> SearchRooms([FromQuery] string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(ApiResponse<List<LocalityDto>>.ErrorResponse("Search term is required"));
                }

                var rooms = await _localityRepository.SearchRoomsByNameAsync(searchTerm);
                var roomDtos = rooms.Select(room => MapToLocalityDto(room, 0)).ToList();

                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(roomDtos,
                    $"Found {roomDtos.Count} rooms matching '{searchTerm}'"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching rooms with term {SearchTerm}", searchTerm);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Search all localities by name (buildings, wards, rooms)
        /// </summary>
        [HttpGet("search")]
        [ProducesResponseType(typeof(ApiResponse<List<LocalityDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<LocalityDto>>>> SearchAllLocalities([FromQuery] string searchTerm, [FromQuery] int? localityTypeId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(ApiResponse<List<LocalityDto>>.ErrorResponse("Search term is required"));
                }

                var localities = await _localityRepository.SearchLocalitiesByNameAsync(searchTerm, localityTypeId);
                var localityDtos = new List<LocalityDto>();

                foreach (var locality in localities)
                {
                    var childCount = locality.LocalityTypeID != LocalityTypes.Room
                        ? await _localityRepository.GetChildCountAsync(locality.LocalityID)
                        : 0;
                    localityDtos.Add(MapToLocalityDto(locality, childCount));
                }

                var typeFilter = localityTypeId.HasValue ? $" of type {LocalityTypes.Names.GetValueOrDefault(localityTypeId.Value, "Unknown")}" : "";
                return Ok(ApiResponse<List<LocalityDto>>.SuccessResponse(localityDtos,
                    $"Found {localityDtos.Count} localities{typeFilter} matching '{searchTerm}'"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching localities with term {SearchTerm} and type {LocalityTypeId}", searchTerm, localityTypeId);
                return StatusCode(500, ApiResponse<List<LocalityDto>>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Update by Name Operations

        /// <summary>
        /// Update building by name
        /// </summary>
        [HttpPut("buildings/update-by-name/{buildingName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuildingByName(string buildingName, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the building by name
                var existingBuilding = await _localityRepository.GetLocalityByNameAsync(buildingName, LocalityTypes.Building);
                if (existingBuilding == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Building with name '{buildingName}' not found"));
                }

                // Check if the new code already exists (excluding current building)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingBuilding.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Building code already exists"));
                }

                // Update the building properties
                existingBuilding.LocalityTitle = updateDto.LocalityTitle;
                existingBuilding.LocalityCode = updateDto.LocalityCode;
                existingBuilding.LocalityDescription = updateDto.LocalityDescription;
                existingBuilding.Facilities = updateDto.Facilities;
                existingBuilding.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingBuilding.IsHomeLeave = updateDto.IsHomeLeave;
                existingBuilding.SequenceNo = updateDto.SequenceNo;
                existingBuilding.IsActive = updateDto.IsActive;
                existingBuilding.UpdatedBy = 1; // TODO: Get from current user
                existingBuilding.UpdatedAt = DateTime.Now;
                existingBuilding.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByNameAsync(buildingName, LocalityTypes.Building, existingBuilding);
                if (success)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(existingBuilding.LocalityID);
                    var buildingDto = MapToLocalityDto(existingBuilding, childCount);
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, $"Building '{buildingName}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building with name {BuildingName}", buildingName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update ward by name
        /// </summary>
        [HttpPut("wards/update-by-name/{wardName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateWardByName(string wardName, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the ward by name
                var existingWard = await _localityRepository.GetLocalityByNameAsync(wardName, LocalityTypes.Ward);
                if (existingWard == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Ward with name '{wardName}' not found"));
                }

                // Check if the new code already exists (excluding current ward)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingWard.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Ward code already exists"));
                }

                // Update the ward properties
                existingWard.LocalityTitle = updateDto.LocalityTitle;
                existingWard.LocalityCode = updateDto.LocalityCode;
                existingWard.LocalityDescription = updateDto.LocalityDescription;
                existingWard.Facilities = updateDto.Facilities;
                existingWard.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingWard.IsHomeLeave = updateDto.IsHomeLeave;
                existingWard.SequenceNo = updateDto.SequenceNo;
                existingWard.IsActive = updateDto.IsActive;
                existingWard.UpdatedBy = 1; // TODO: Get from current user
                existingWard.UpdatedAt = DateTime.Now;
                existingWard.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByNameAsync(wardName, LocalityTypes.Ward, existingWard);
                if (success)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(existingWard.LocalityID);
                    var wardDto = MapToLocalityDto(existingWard, childCount);
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, $"Ward '{wardName}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update ward"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ward with name {WardName}", wardName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update room by name
        /// </summary>
        [HttpPut("rooms/update-by-name/{roomName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateRoomByName(string roomName, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the room by name
                var existingRoom = await _localityRepository.GetLocalityByNameAsync(roomName, LocalityTypes.Room);
                if (existingRoom == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Room with name '{roomName}' not found"));
                }

                // Check if the new code already exists (excluding current room)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingRoom.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Room code already exists"));
                }

                // Update the room properties
                existingRoom.LocalityTitle = updateDto.LocalityTitle;
                existingRoom.LocalityCode = updateDto.LocalityCode;
                existingRoom.LocalityDescription = updateDto.LocalityDescription;
                existingRoom.Facilities = updateDto.Facilities;
                existingRoom.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingRoom.IsHomeLeave = updateDto.IsHomeLeave;
                existingRoom.SequenceNo = updateDto.SequenceNo;
                existingRoom.IsActive = updateDto.IsActive;
                existingRoom.UpdatedBy = 1; // TODO: Get from current user
                existingRoom.UpdatedAt = DateTime.Now;
                existingRoom.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByNameAsync(roomName, LocalityTypes.Room, existingRoom);
                if (success)
                {
                    var roomDto = MapToLocalityDto(existingRoom, 0); // Rooms don't have children
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, $"Room '{roomName}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update room"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating room with name {RoomName}", roomName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Delete by Name Operations

        /// <summary>
        /// Delete ward by name
        /// </summary>
        [HttpDelete("wards/delete-by-name/{wardName}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteWardByName(string wardName)
        {
            try
            {
                // First, check if ward exists
                var existingWard = await _localityRepository.GetLocalityByNameAsync(wardName, LocalityTypes.Ward);
                if (existingWard == null)
                {
                    return NotFound(ApiResponse<bool>.ErrorResponse($"Ward with name '{wardName}' not found"));
                }

                // Check if ward has rooms
                var childCount = await _localityRepository.GetChildCountAsync(existingWard.LocalityID);
                if (childCount > 0)
                {
                    return BadRequest(ApiResponse<bool>.ErrorResponse("Cannot delete ward that contains rooms. Please remove all rooms first."));
                }

                var success = await _localityRepository.DeleteLocalityByNameAsync(wardName, LocalityTypes.Ward);
                if (success)
                {
                    return Ok(ApiResponse<bool>.SuccessResponse(true, $"Ward '{wardName}' deleted successfully"));
                }

                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Failed to delete ward"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ward with name {WardName}", wardName);
                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Delete room by name
        /// </summary>
        [HttpDelete("rooms/delete-by-name/{roomName}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteRoomByName(string roomName)
        {
            try
            {
                // First, check if room exists
                var existingRoom = await _localityRepository.GetLocalityByNameAsync(roomName, LocalityTypes.Room);
                if (existingRoom == null)
                {
                    return NotFound(ApiResponse<bool>.ErrorResponse($"Room with name '{roomName}' not found"));
                }

                var success = await _localityRepository.DeleteLocalityByNameAsync(roomName, LocalityTypes.Room);
                if (success)
                {
                    return Ok(ApiResponse<bool>.SuccessResponse(true, $"Room '{roomName}' deleted successfully"));
                }

                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Failed to delete room"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting room with name {RoomName}", roomName);
                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Simple Create Operations

        /// <summary>
        /// Create new building with minimal input (just name and description)
        /// </summary>
        [HttpPost("buildings/simple")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> CreateBuildingSimple([FromBody] SimpleCreateLocalityDto createDto)
        {
            try
            {
                // Generate a simple code based on name
                var localityCode = GenerateLocalityCode(createDto.LocalityTitle, "BLD");

                // Check if code already exists
                if (await _localityRepository.LocalityCodeExistsAsync(localityCode))
                {
                    localityCode = GenerateUniqueLocalityCode(createDto.LocalityTitle, "BLD");
                }

                var locality = new Locality
                {
                    LocalityTitle = createDto.LocalityTitle,
                    LocalityCode = localityCode,
                    LocalityDescription = createDto.LocalityDescription,
                    LocalityTypeID = LocalityTypes.Building,
                    ParentLocalityID = null, // Buildings have no parent
                    InsertedBy = 1, // Default user
                    UpdatedBy = 1,
                    UserLoggingID = 1,
                    InsertedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    IsActive = true,
                    IsDeleted = false,
                    IsOccupied = false
                };

                var newId = await _localityRepository.CreateLocalityAsync(locality);
                var createdBuilding = await _localityRepository.GetLocalityByIdAsync(newId);

                if (createdBuilding != null)
                {
                    var buildingDto = MapToLocalityDto(createdBuilding, 0);
                    return CreatedAtAction(nameof(GetBuilding), new { id = newId },
                        ApiResponse<LocalityDto>.SuccessResponse(buildingDto, "Building created successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to create building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating building");
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Create new ward with minimal input (name, parent building name, and description)
        /// </summary>
        [HttpPost("wards/simple")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> CreateWardSimple([FromBody] SimpleCreateLocalityDto createDto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(createDto.ParentLocalityName))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Parent building name is required for creating a ward"));
                }

                // Find parent building
                var parentBuilding = await _localityRepository.GetLocalityByNameAsync(createDto.ParentLocalityName, LocalityTypes.Building);
                if (parentBuilding == null)
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse($"Building '{createDto.ParentLocalityName}' not found"));
                }

                // Generate a simple code based on name
                var localityCode = GenerateLocalityCode(createDto.LocalityTitle, "WRD");

                // Check if code already exists
                if (await _localityRepository.LocalityCodeExistsAsync(localityCode))
                {
                    localityCode = GenerateUniqueLocalityCode(createDto.LocalityTitle, "WRD");
                }

                var locality = new Locality
                {
                    LocalityTitle = createDto.LocalityTitle,
                    LocalityCode = localityCode,
                    LocalityDescription = createDto.LocalityDescription,
                    LocalityTypeID = LocalityTypes.Ward,
                    ParentLocalityID = parentBuilding.LocalityID,
                    InsertedBy = 1, // Default user
                    UpdatedBy = 1,
                    UserLoggingID = 1,
                    InsertedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    IsActive = true,
                    IsDeleted = false,
                    IsOccupied = false
                };

                var newId = await _localityRepository.CreateLocalityAsync(locality);
                var createdWard = await _localityRepository.GetLocalityByIdAsync(newId);

                if (createdWard != null)
                {
                    var wardDto = MapToLocalityDto(createdWard, 0);
                    return CreatedAtAction(nameof(GetBuilding), new { id = newId },
                        ApiResponse<LocalityDto>.SuccessResponse(wardDto, "Ward created successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to create ward"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating ward");
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Create new room with minimal input (name, parent ward name, and description)
        /// </summary>
        [HttpPost("rooms/simple")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> CreateRoomSimple([FromBody] SimpleCreateLocalityDto createDto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(createDto.ParentLocalityName))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Parent ward name is required for creating a room"));
                }

                // Find parent ward
                var parentWard = await _localityRepository.GetLocalityByNameAsync(createDto.ParentLocalityName, LocalityTypes.Ward);
                if (parentWard == null)
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse($"Ward '{createDto.ParentLocalityName}' not found"));
                }

                // Generate a simple code based on name
                var localityCode = GenerateLocalityCode(createDto.LocalityTitle, "RM");

                // Check if code already exists
                if (await _localityRepository.LocalityCodeExistsAsync(localityCode))
                {
                    localityCode = GenerateUniqueLocalityCode(createDto.LocalityTitle, "RM");
                }

                var locality = new Locality
                {
                    LocalityTitle = createDto.LocalityTitle,
                    LocalityCode = localityCode,
                    LocalityDescription = createDto.LocalityDescription,
                    LocalityTypeID = LocalityTypes.Room,
                    ParentLocalityID = parentWard.LocalityID,
                    InsertedBy = 1, // Default user
                    UpdatedBy = 1,
                    UserLoggingID = 1,
                    InsertedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    IsActive = true,
                    IsDeleted = false,
                    IsOccupied = false
                };

                var newId = await _localityRepository.CreateLocalityAsync(locality);
                var createdRoom = await _localityRepository.GetLocalityByIdAsync(newId);

                if (createdRoom != null)
                {
                    var roomDto = MapToLocalityDto(createdRoom, 0);
                    return CreatedAtAction(nameof(GetBuilding), new { id = newId },
                        ApiResponse<LocalityDto>.SuccessResponse(roomDto, "Room created successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to create room"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating room");
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Update by Code Operations

        /// <summary>
        /// Update building by code
        /// </summary>
        [HttpPut("buildings/update-by-code/{buildingCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuildingByCode(string buildingCode, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the building by code
                var existingBuilding = await _localityRepository.GetLocalityByCodeAsync(buildingCode, LocalityTypes.Building);
                if (existingBuilding == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Building with code '{buildingCode}' not found"));
                }

                // Check if the new code already exists (excluding current building)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingBuilding.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Building code already exists"));
                }

                // Update the building properties
                existingBuilding.LocalityTitle = updateDto.LocalityTitle;
                existingBuilding.LocalityCode = updateDto.LocalityCode;
                existingBuilding.LocalityDescription = updateDto.LocalityDescription;
                existingBuilding.Facilities = updateDto.Facilities;
                existingBuilding.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingBuilding.IsHomeLeave = updateDto.IsHomeLeave;
                existingBuilding.SequenceNo = updateDto.SequenceNo;
                existingBuilding.IsActive = updateDto.IsActive;
                existingBuilding.UpdatedBy = 1; // TODO: Get from current user
                existingBuilding.UpdatedAt = DateTime.Now;
                existingBuilding.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByCodeAsync(buildingCode, LocalityTypes.Building, existingBuilding);
                if (success)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(existingBuilding.LocalityID);
                    var buildingDto = MapToLocalityDto(existingBuilding, childCount);
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, $"Building with code '{buildingCode}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building with code {BuildingCode}", buildingCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update ward by code
        /// </summary>
        [HttpPut("wards/update-by-code/{wardCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateWardByCode(string wardCode, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the ward by code
                var existingWard = await _localityRepository.GetLocalityByCodeAsync(wardCode, LocalityTypes.Ward);
                if (existingWard == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Ward with code '{wardCode}' not found"));
                }

                // Check if the new code already exists (excluding current ward)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingWard.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Ward code already exists"));
                }

                // Update the ward properties
                existingWard.LocalityTitle = updateDto.LocalityTitle;
                existingWard.LocalityCode = updateDto.LocalityCode;
                existingWard.LocalityDescription = updateDto.LocalityDescription;
                existingWard.Facilities = updateDto.Facilities;
                existingWard.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingWard.IsHomeLeave = updateDto.IsHomeLeave;
                existingWard.SequenceNo = updateDto.SequenceNo;
                existingWard.IsActive = updateDto.IsActive;
                existingWard.UpdatedBy = 1; // TODO: Get from current user
                existingWard.UpdatedAt = DateTime.Now;
                existingWard.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByCodeAsync(wardCode, LocalityTypes.Ward, existingWard);
                if (success)
                {
                    var childCount = await _localityRepository.GetChildCountAsync(existingWard.LocalityID);
                    var wardDto = MapToLocalityDto(existingWard, childCount);
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, $"Ward with code '{wardCode}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update ward"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ward with code {WardCode}", wardCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update room by code
        /// </summary>
        [HttpPut("rooms/update-by-code/{roomCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateRoomByCode(string roomCode, [FromBody] UpdateLocalityDto updateDto)
        {
            try
            {
                // First, find the room by code
                var existingRoom = await _localityRepository.GetLocalityByCodeAsync(roomCode, LocalityTypes.Room);
                if (existingRoom == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Room with code '{roomCode}' not found"));
                }

                // Check if the new code already exists (excluding current room)
                if (await _localityRepository.LocalityCodeExistsAsync(updateDto.LocalityCode, existingRoom.LocalityID))
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse("Room code already exists"));
                }

                // Update the room properties
                existingRoom.LocalityTitle = updateDto.LocalityTitle;
                existingRoom.LocalityCode = updateDto.LocalityCode;
                existingRoom.LocalityDescription = updateDto.LocalityDescription;
                existingRoom.Facilities = updateDto.Facilities;
                existingRoom.IsMultipleRoom = updateDto.IsMultipleRoom;
                existingRoom.IsHomeLeave = updateDto.IsHomeLeave;
                existingRoom.SequenceNo = updateDto.SequenceNo;
                existingRoom.IsActive = updateDto.IsActive;
                existingRoom.UpdatedBy = 1; // TODO: Get from current user
                existingRoom.UpdatedAt = DateTime.Now;
                existingRoom.UserLoggingID = 1; // TODO: Get from current user

                var success = await _localityRepository.UpdateLocalityByCodeAsync(roomCode, LocalityTypes.Room, existingRoom);
                if (success)
                {
                    var roomDto = MapToLocalityDto(existingRoom, 0); // Rooms don't have children
                    return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, $"Room with code '{roomCode}' updated successfully"));
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update room"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating room with code {RoomCode}", roomCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Parent Relationship Updates

        /// <summary>
        /// Move room to a different ward
        /// </summary>
        [HttpPut("rooms/{roomId}/move-to-ward")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> MoveRoomToWard(int roomId, [FromBody] UpdateRoomWardDto updateDto)
        {
            try
            {
                // Check if room exists
                var room = await _localityRepository.GetLocalityByIdAsync(roomId);
                if (room == null || room.LocalityTypeID != LocalityTypes.Room)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Room not found"));
                }

                // Find the new ward by name
                var newWard = await _localityRepository.GetLocalityByNameAsync(updateDto.NewWardName, LocalityTypes.Ward);
                if (newWard == null)
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse($"Ward '{updateDto.NewWardName}' not found"));
                }

                var success = await _localityRepository.UpdateRoomWardAsync(roomId, newWard.LocalityID);
                if (success)
                {
                    // Get updated room data
                    var updatedRoom = await _localityRepository.GetLocalityByIdAsync(roomId);
                    if (updatedRoom != null)
                    {
                        var roomDto = MapToLocalityDto(updatedRoom, 0);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, $"Room moved to ward '{updateDto.NewWardName}' successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to move room to ward"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving room {RoomId} to ward {WardName}", roomId, updateDto.NewWardName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Move ward to a different building
        /// </summary>
        [HttpPut("wards/{wardId}/move-to-building")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 400)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> MoveWardToBuilding(int wardId, [FromBody] UpdateWardBuildingDto updateDto)
        {
            try
            {
                // Check if ward exists
                var ward = await _localityRepository.GetLocalityByIdAsync(wardId);
                if (ward == null || ward.LocalityTypeID != LocalityTypes.Ward)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Ward not found"));
                }

                // Find the new building by name
                var newBuilding = await _localityRepository.GetLocalityByNameAsync(updateDto.NewBuildingName, LocalityTypes.Building);
                if (newBuilding == null)
                {
                    return BadRequest(ApiResponse<LocalityDto>.ErrorResponse($"Building '{updateDto.NewBuildingName}' not found"));
                }

                var success = await _localityRepository.UpdateWardBuildingAsync(wardId, newBuilding.LocalityID);
                if (success)
                {
                    // Get updated ward data
                    var updatedWard = await _localityRepository.GetLocalityByIdAsync(wardId);
                    if (updatedWard != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedWard.LocalityID);
                        var wardDto = MapToLocalityDto(updatedWard, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, $"Ward moved to building '{updateDto.NewBuildingName}' successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to move ward to building"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving ward {WardId} to building {BuildingName}", wardId, updateDto.NewBuildingName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Description Update Operations

        /// <summary>
        /// Update building description by ID
        /// </summary>
        [HttpPatch("buildings/{buildingId}/description")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuildingDescription(int buildingId, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if building exists
                var building = await _localityRepository.GetLocalityByIdAsync(buildingId);
                if (building == null || building.LocalityTypeID != LocalityTypes.Building)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Building not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionAsync(buildingId, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated building data
                    var updatedBuilding = await _localityRepository.GetLocalityByIdAsync(buildingId);
                    if (updatedBuilding != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedBuilding.LocalityID);
                        var buildingDto = MapToLocalityDto(updatedBuilding, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, "Building description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building description for ID {BuildingId}", buildingId);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update building description by name
        /// </summary>
        [HttpPatch("buildings/update-description-by-name/{buildingName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuildingDescriptionByName(string buildingName, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if building exists
                var building = await _localityRepository.GetLocalityByNameAsync(buildingName, LocalityTypes.Building);
                if (building == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Building with name '{buildingName}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByNameAsync(buildingName, LocalityTypes.Building, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated building data
                    var updatedBuilding = await _localityRepository.GetLocalityByNameAsync(buildingName, LocalityTypes.Building);
                    if (updatedBuilding != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedBuilding.LocalityID);
                        var buildingDto = MapToLocalityDto(updatedBuilding, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, $"Building '{buildingName}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building description for name {BuildingName}", buildingName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update building description by code
        /// </summary>
        [HttpPatch("buildings/update-description-by-code/{buildingCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateBuildingDescriptionByCode(string buildingCode, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if building exists
                var building = await _localityRepository.GetLocalityByCodeAsync(buildingCode, LocalityTypes.Building);
                if (building == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Building with code '{buildingCode}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByCodeAsync(buildingCode, LocalityTypes.Building, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated building data
                    var updatedBuilding = await _localityRepository.GetLocalityByCodeAsync(buildingCode, LocalityTypes.Building);
                    if (updatedBuilding != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedBuilding.LocalityID);
                        var buildingDto = MapToLocalityDto(updatedBuilding, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(buildingDto, $"Building with code '{buildingCode}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update building description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating building description for code {BuildingCode}", buildingCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update ward description by ID
        /// </summary>
        [HttpPatch("wards/{wardId}/description")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateWardDescription(int wardId, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if ward exists
                var ward = await _localityRepository.GetLocalityByIdAsync(wardId);
                if (ward == null || ward.LocalityTypeID != LocalityTypes.Ward)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Ward not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionAsync(wardId, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated ward data
                    var updatedWard = await _localityRepository.GetLocalityByIdAsync(wardId);
                    if (updatedWard != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedWard.LocalityID);
                        var wardDto = MapToLocalityDto(updatedWard, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, "Ward description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update ward description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ward description for ID {WardId}", wardId);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update ward description by name
        /// </summary>
        [HttpPatch("wards/update-description-by-name/{wardName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateWardDescriptionByName(string wardName, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if ward exists
                var ward = await _localityRepository.GetLocalityByNameAsync(wardName, LocalityTypes.Ward);
                if (ward == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Ward with name '{wardName}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByNameAsync(wardName, LocalityTypes.Ward, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated ward data
                    var updatedWard = await _localityRepository.GetLocalityByNameAsync(wardName, LocalityTypes.Ward);
                    if (updatedWard != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedWard.LocalityID);
                        var wardDto = MapToLocalityDto(updatedWard, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, $"Ward '{wardName}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update ward description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ward description for name {WardName}", wardName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update ward description by code
        /// </summary>
        [HttpPatch("wards/update-description-by-code/{wardCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateWardDescriptionByCode(string wardCode, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if ward exists
                var ward = await _localityRepository.GetLocalityByCodeAsync(wardCode, LocalityTypes.Ward);
                if (ward == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Ward with code '{wardCode}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByCodeAsync(wardCode, LocalityTypes.Ward, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated ward data
                    var updatedWard = await _localityRepository.GetLocalityByCodeAsync(wardCode, LocalityTypes.Ward);
                    if (updatedWard != null)
                    {
                        var childCount = await _localityRepository.GetChildCountAsync(updatedWard.LocalityID);
                        var wardDto = MapToLocalityDto(updatedWard, childCount);
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(wardDto, $"Ward with code '{wardCode}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update ward description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ward description for code {WardCode}", wardCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update room description by ID
        /// </summary>
        [HttpPatch("rooms/{roomId}/description")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateRoomDescription(int roomId, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if room exists
                var room = await _localityRepository.GetLocalityByIdAsync(roomId);
                if (room == null || room.LocalityTypeID != LocalityTypes.Room)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse("Room not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionAsync(roomId, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated room data
                    var updatedRoom = await _localityRepository.GetLocalityByIdAsync(roomId);
                    if (updatedRoom != null)
                    {
                        var roomDto = MapToLocalityDto(updatedRoom, 0); // Rooms don't have children
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, "Room description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update room description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating room description for ID {RoomId}", roomId);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update room description by name
        /// </summary>
        [HttpPatch("rooms/update-description-by-name/{roomName}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateRoomDescriptionByName(string roomName, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if room exists
                var room = await _localityRepository.GetLocalityByNameAsync(roomName, LocalityTypes.Room);
                if (room == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Room with name '{roomName}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByNameAsync(roomName, LocalityTypes.Room, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated room data
                    var updatedRoom = await _localityRepository.GetLocalityByNameAsync(roomName, LocalityTypes.Room);
                    if (updatedRoom != null)
                    {
                        var roomDto = MapToLocalityDto(updatedRoom, 0); // Rooms don't have children
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, $"Room '{roomName}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update room description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating room description for name {RoomName}", roomName);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update room description by code
        /// </summary>
        [HttpPatch("rooms/update-description-by-code/{roomCode}")]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<LocalityDto>), 404)]
        public async Task<ActionResult<ApiResponse<LocalityDto>>> UpdateRoomDescriptionByCode(string roomCode, [FromBody] UpdateDescriptionDto updateDto)
        {
            try
            {
                // Check if room exists
                var room = await _localityRepository.GetLocalityByCodeAsync(roomCode, LocalityTypes.Room);
                if (room == null)
                {
                    return NotFound(ApiResponse<LocalityDto>.ErrorResponse($"Room with code '{roomCode}' not found"));
                }

                var success = await _localityRepository.UpdateLocalityDescriptionByCodeAsync(roomCode, LocalityTypes.Room, updateDto.LocalityDescription ?? string.Empty);
                if (success)
                {
                    // Get updated room data
                    var updatedRoom = await _localityRepository.GetLocalityByCodeAsync(roomCode, LocalityTypes.Room);
                    if (updatedRoom != null)
                    {
                        var roomDto = MapToLocalityDto(updatedRoom, 0); // Rooms don't have children
                        return Ok(ApiResponse<LocalityDto>.SuccessResponse(roomDto, $"Room with code '{roomCode}' description updated successfully"));
                    }
                }

                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Failed to update room description"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating room description for code {RoomCode}", roomCode);
                return StatusCode(500, ApiResponse<LocalityDto>.ErrorResponse("Internal server error"));
            }
        }

        #endregion

        #region Helper Methods

        private static string GenerateLocalityCode(string localityTitle, string prefix)
        {
            // Remove spaces and special characters, take first 3 characters, add prefix
            var cleanTitle = new string(localityTitle.Where(char.IsLetterOrDigit).ToArray());
            var titlePart = cleanTitle.Length >= 3 ? cleanTitle.Substring(0, 3).ToUpper() : cleanTitle.ToUpper().PadRight(3, '0');
            return $"{prefix}{titlePart}";
        }

        private string GenerateUniqueLocalityCode(string localityTitle, string prefix)
        {
            var baseCode = GenerateLocalityCode(localityTitle, prefix);
            var counter = 1;
            var uniqueCode = baseCode;

            // Keep trying until we find a unique code
            while (_localityRepository.LocalityCodeExistsAsync(uniqueCode).Result)
            {
                uniqueCode = $"{baseCode}{counter:D2}";
                counter++;
                if (counter > 99) // Safety check
                {
                    uniqueCode = $"{prefix}{DateTime.Now.Ticks.ToString().Substring(0, 3)}";
                    break;
                }
            }

            return uniqueCode;
        }

        private static LocalityDto MapToLocalityDto(Locality locality, int childCount)
        {
            return new LocalityDto
            {
                LocalityID = locality.LocalityID,
                LocalityTitle = locality.LocalityTitle,
                LocalityCode = locality.LocalityCode,
                LocalityDescription = locality.LocalityDescription,
                LocalityTypeID = locality.LocalityTypeID,
                LocalityTypeName = LocalityTypes.Names.GetValueOrDefault(locality.LocalityTypeID, "Unknown"),
                ParentLocalityID = locality.ParentLocalityID,
                ParentLocalityTitle = locality.ParentTitle,
                IsActive = locality.IsActive,
                IsOccupied = locality.IsOccupied,
                Facilities = locality.Facilities,
                IsMultipleRoom = locality.IsMultipleRoom,
                IsHomeLeave = locality.IsHomeLeave,
                SequenceNo = locality.SequenceNo,
                InsertedAt = locality.InsertedAt,
                UpdatedAt = locality.UpdatedAt,
                ChildCount = childCount
            };
        }

        private static Locality MapToLocality(CreateLocalityDto createDto)
        {
            return new Locality
            {
                LocalityTitle = createDto.LocalityTitle,
                LocalityCode = createDto.LocalityCode,
                LocalityDescription = createDto.LocalityDescription,
                LocalityTypeID = createDto.LocalityTypeID,
                ParentLocalityID = createDto.ParentLocalityID,
                PracticeID = createDto.PracticeID,
                PracticeLocationID = createDto.PracticeLocationID,
                SequenceNo = createDto.SequenceNo,
                Facilities = createDto.Facilities,
                IsMultipleRoom = createDto.IsMultipleRoom,
                IsHomeLeave = createDto.IsHomeLeave
            };
        }

        #endregion
    }
}
