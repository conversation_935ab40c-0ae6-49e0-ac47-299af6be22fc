using IPU.API.Models;
using IPU.API.DTOs;
using Microsoft.Data.SqlClient;
using System.Data;

namespace IPU.API.Data
{
    public class PatientRepository : IPatientRepository
    {
        private readonly string _connectionString;

        public PatientRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration));
        }

        public async Task<int> GetTotalInpatientsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetTotalInpatients]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result ?? 0);
        }

        public async Task<int> GetAdmittedPatientsCountAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetAdmittedPatientsCount]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result ?? 0);
        }

        public async Task<PatientAdmissionStatusDto> CheckPatientAdmissionStatusAsync(string patientName)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspCheckPatientAdmissionStatus]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@PatientName", patientName);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new PatientAdmissionStatusDto
                {
                    PatientName = patientName,
                    IsAdmitted = reader.GetBoolean("IsAdmitted"),
                    AdmissionDate = reader.IsDBNull("AdmissionDate") ? null : reader.GetDateTime("AdmissionDate"),
                    RoomName = reader.IsDBNull("RoomName") ? null : reader.GetString("RoomName"),
                    AdmissionType = reader.IsDBNull("AdmissionType") ? null : reader.GetString("AdmissionType"),
                    IsParked = reader.IsDBNull("IsParked") ? null : reader.GetBoolean("IsParked"),
                    Message = reader.GetBoolean("IsAdmitted") ? "Patient is currently admitted" : "Patient is not admitted"
                };
            }

            return new PatientAdmissionStatusDto
            {
                PatientName = patientName,
                IsAdmitted = false,
                Message = "Patient not found"
            };
        }

        public async Task<List<DischargeDto>> GetDischargedPatientsByDateAsync(DateTime date)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetDischargedPatientsByDate]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@DischargeDate", date.Date);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var discharges = new List<DischargeDto>();
            while (await reader.ReadAsync())
            {
                discharges.Add(MapToDischargeDto(reader));
            }

            return discharges;
        }

        public async Task<List<PatientAdmissionDto>> GetAdmittedPatientsByDateAsync(DateTime date)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetAdmittedPatientsByDate]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@AdmissionDate", date.Date);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var admissions = new List<PatientAdmissionDto>();
            while (await reader.ReadAsync())
            {
                admissions.Add(MapToPatientAdmissionDto(reader));
            }

            return admissions;
        }

        public async Task<List<PatientAdmissionDto>> GetMedicallyDischargedPatientsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetMedicallyDischargedPatients]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var admissions = new List<PatientAdmissionDto>();
            while (await reader.ReadAsync())
            {
                admissions.Add(MapToPatientAdmissionDto(reader));
            }

            return admissions;
        }

        public async Task<List<RoomAllocationDto>> GetTodayAdmittedPatientsRoomAllocationAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetTodayAdmittedPatientsRoomAllocation]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var allocations = new List<RoomAllocationDto>();
            while (await reader.ReadAsync())
            {
                allocations.Add(MapToRoomAllocationDto(reader));
            }

            return allocations;
        }

        public async Task<List<AvailableRoomDto>> GetAvailableRoomsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetAvailableRooms]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var rooms = new List<AvailableRoomDto>();
            while (await reader.ReadAsync())
            {
                rooms.Add(MapToAvailableRoomDto(reader));
            }

            return rooms;
        }

        public async Task<bool> UpdatePatientRoomAllocationAsync(int patientAdmissionId, int newRoomId, string? comments = null)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspUpdatePatientRoomAllocation]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@PatientAdmissionID", patientAdmissionId);
            command.Parameters.AddWithValue("@NewRoomID", newRoomId);
            command.Parameters.AddWithValue("@Comments", comments ?? (object)DBNull.Value);

            await connection.OpenAsync();
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        public async Task<Patient?> GetPatientByIdAsync(int patientId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetPatientById]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@PatientID", patientId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapToPatient(reader);
            }

            return null;
        }

        public async Task<Patient?> GetPatientByNameAsync(string patientName)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetPatientByName]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@PatientName", patientName);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapToPatient(reader);
            }

            return null;
        }

        public async Task<List<PatientAdmissionDto>> GetActiveAdmissionsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetActiveAdmissions]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            var admissions = new List<PatientAdmissionDto>();
            while (await reader.ReadAsync())
            {
                admissions.Add(MapToPatientAdmissionDto(reader));
            }

            return admissions;
        }

        public async Task<PatientAdmissionDto?> GetPatientAdmissionByIdAsync(int admissionId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetPatientAdmissionById]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@AdmissionID", admissionId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapToPatientAdmissionDto(reader);
            }

            return null;
        }

        public async Task<List<DischargeDto>> GetTodayDischargedPatientsAsync()
        {
            return await GetDischargedPatientsByDateAsync(DateTime.Today);
        }

        public async Task<DischargeDto?> GetDischargeByIdAsync(int dischargeId)
        {
            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("[IPU].[uspGetDischargeById]", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@DischargeID", dischargeId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapToDischargeDto(reader);
            }

            return null;
        }

        // Helper mapping methods
        private static DischargeDto MapToDischargeDto(SqlDataReader reader)
        {
            return new DischargeDto
            {
                DischargeID = reader.GetInt32("DischargeID"),
                PatientID = reader.IsDBNull("PatientID") ? null : reader.GetInt32("PatientID"),
                PatientName = reader.IsDBNull("PatientName") ? null : reader.GetString("PatientName"),
                ChartNumber = reader.IsDBNull("ChartNumber") ? null : reader.GetString("ChartNumber"),
                DischargeDate = reader.IsDBNull("DischargeDate") ? null : reader.GetDateTime("DischargeDate"),
                MedicalDischargeDate = reader.IsDBNull("MedicalDischargeDate") ? null : reader.GetDateTime("MedicalDischargeDate"),
                PhysicalDischargeDate = reader.IsDBNull("PhysicalDischargeDate") ? null : reader.GetDateTime("PhysicalDischargeDate"),
                DischargeReasonID = reader.GetInt32("DischargeReasonID"),
                Outcome = reader.IsDBNull("Outcome") ? null : reader.GetString("Outcome"),
                DischargedDestination = reader.IsDBNull("DischargedDestination") ? null : reader.GetString("DischargedDestination"),
                IsParked = reader.IsDBNull("IsParked") ? null : reader.GetBoolean("IsParked"),
                IsActive = reader.GetBoolean("IsActive"),
                InsertedAt = reader.IsDBNull("InsertedAt") ? null : reader.GetDateTime("InsertedAt"),
                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : reader.GetDateTime("UpdatedAt")
            };
        }

        private static RoomAllocationDto MapToRoomAllocationDto(SqlDataReader reader)
        {
            return new RoomAllocationDto
            {
                PatientAdmissionID = reader.GetInt32("PatientAdmissionID"),
                PatientID = reader.GetInt32("PatientID"),
                PatientName = reader.IsDBNull("PatientName") ? null : reader.GetString("PatientName"),
                ChartNumber = reader.IsDBNull("ChartNumber") ? null : reader.GetString("ChartNumber"),
                AdmissionDate = reader.IsDBNull("AdmissionDate") ? null : reader.GetDateTime("AdmissionDate"),
                CurrentRoomID = reader.IsDBNull("CurrentRoomID") ? null : reader.GetInt32("CurrentRoomID"),
                CurrentRoomName = reader.IsDBNull("CurrentRoomName") ? null : reader.GetString("CurrentRoomName"),
                CurrentRoomCode = reader.IsDBNull("CurrentRoomCode") ? null : reader.GetString("CurrentRoomCode"),
                IsCurrentRoomOccupied = reader.IsDBNull("IsCurrentRoomOccupied") ? null : reader.GetBoolean("IsCurrentRoomOccupied")
            };
        }

        private static AvailableRoomDto MapToAvailableRoomDto(SqlDataReader reader)
        {
            return new AvailableRoomDto
            {
                LocalityID = reader.GetInt32("LocalityID"),
                LocalityTitle = reader.GetString("LocalityTitle"),
                LocalityCode = reader.GetString("LocalityCode"),
                LocalityDescription = reader.IsDBNull("LocalityDescription") ? null : reader.GetString("LocalityDescription"),
                IsOccupied = reader.GetBoolean("IsOccupied"),
                WardName = reader.IsDBNull("WardName") ? null : reader.GetString("WardName"),
                BuildingName = reader.IsDBNull("BuildingName") ? null : reader.GetString("BuildingName")
            };
        }

        private static Patient MapToPatient(SqlDataReader reader)
        {
            return new Patient
            {
                PatientID = reader.GetInt32("PatientID"),
                ChartNumber = reader.IsDBNull("ChartNumber") ? null : reader.GetString("ChartNumber"),
                IsAdmitted = reader.GetBoolean("IsAdmitted"),
                pFirstName = reader.IsDBNull("pFirstName") ? null : reader.GetString("pFirstName"),
                pMiddleName = reader.IsDBNull("pMiddleName") ? null : reader.GetString("pMiddleName"),
                pFamilyName = reader.IsDBNull("pFamilyName") ? null : reader.GetString("pFamilyName"),
                pFullName = reader.IsDBNull("pFullName") ? null : reader.GetString("pFullName"),
                pPreferredName = reader.IsDBNull("pPreferredName") ? null : reader.GetString("pPreferredName"),
                pDOB = reader.IsDBNull("pDOB") ? null : reader.GetDateTime("pDOB"),
                pCellNumber = reader.IsDBNull("pCellNumber") ? null : reader.GetString("pCellNumber"),
                pEmail = reader.IsDBNull("pEmail") ? null : reader.GetString("pEmail"),
                pNHINumber = reader.IsDBNull("pNHINumber") ? null : reader.GetString("pNHINumber"),
                pGenderID = reader.IsDBNull("pGenderID") ? null : reader.GetInt32("pGenderID"),
                pIsActive = reader.GetBoolean("pIsActive"),
                pIsDeleted = reader.GetBoolean("pIsDeleted"),
                InsertedAt = reader.IsDBNull("InsertedAt") ? null : reader.GetDateTime("InsertedAt"),
                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : reader.GetDateTime("UpdatedAt")
            };
        }

        private static PatientAdmissionDto MapToPatientAdmissionDto(SqlDataReader reader)
        {
            return new PatientAdmissionDto
            {
                PatientAdmissionID = reader.GetInt32("PatientAdmissionID"),
                PatientID = reader.IsDBNull("PatientID") ? null : reader.GetInt32("PatientID"),
                PatientName = reader.IsDBNull("PatientName") ? null : reader.GetString("PatientName"),
                ChartNumber = reader.IsDBNull("ChartNumber") ? null : reader.GetString("ChartNumber"),
                AdmissionDate = reader.IsDBNull("AdmissionDate") ? null : reader.GetDateTime("AdmissionDate"),
                AdmissionTime = reader.IsDBNull("AdmissionTime") ? null : reader.GetDateTime("AdmissionTime"),
                LocalityID = reader.IsDBNull("LocalityID") ? null : reader.GetInt32("LocalityID"),
                RoomName = reader.IsDBNull("RoomName") ? null : reader.GetString("RoomName"),
                RoomCode = reader.IsDBNull("RoomCode") ? null : reader.GetString("RoomCode"),
                AssignedNurseID = reader.IsDBNull("AssignedNurseID") ? null : reader.GetInt32("AssignedNurseID"),
                AssignedDoctorID = reader.IsDBNull("AssignedDoctorID") ? null : reader.GetInt32("AssignedDoctorID"),
                AdmissionType = reader.IsDBNull("AdmissionType") ? null : reader.GetString("AdmissionType"),
                Comments = reader.IsDBNull("Comments") ? null : reader.GetString("Comments"),
                IsActive = reader.GetBoolean("IsActive"),
                IsParked = reader.IsDBNull("IsParked") ? null : reader.GetBoolean("IsParked"),
                DischargeID = reader.IsDBNull("DischargeID") ? null : reader.GetInt32("DischargeID"),
                InsertedAt = reader.IsDBNull("InsertedAt") ? null : reader.GetDateTime("InsertedAt"),
                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : reader.GetDateTime("UpdatedAt")
            };
        }
    }
}
