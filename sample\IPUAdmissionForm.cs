﻿using MHN.Entity.MapOfMedicine;
using MHN.Entity.Referrals;
using MHN.Entity.UserManagement;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace MHN.Entity.ServiceTemplate
{
    [DataContract]
    public class IPUAdmissionForm
    {
        public List<IPUAdmissionForm> Objlist { get; set; }

        public int FromTab { get; set; }
        [DataMember]
        public int AdmissionFormID { get; set; }
        [DataMember]
        public int PatientAdmissionID { get; set; }
        public int IsViewed { get; set; }
        [DataMember]
        public string PatientID { get; set; }
        [DataMember]
        public DateTime AdmissionDate { get; set; }
        public DateTime AdmissionTime { get; set; }
        public string AdmissionTimestring { get; set; }
        //public DateTime MedicalDischargetime { get; set; }
        public string MedicalDischargeTimestring { get; set; }
        public string DischargingCarer { get; set; }
        public string Deschargedestination { get; set; }

        [DataMember]
        [DataType(DataType.Time)]
        [DisplayFormat(DataFormatString = "{hh:mm tt}")]
        public DateTime MedicalDischargetime { get; set; }

        public DateTime MedicalDischargeDate { get; set; }

        [DataMember]
        public DateTime HomeLeaveDate { get; set; }

        [DataMember]
        [DataType(DataType.Time)]
        [DisplayFormat(DataFormatString = "{hh:mm tt}")]
        public DateTime HomeLeaveTime { get; set; }
        public string AdmissionDateString { get; set; }

        [DataMember]
        public bool IsPreviousAdmission { get; set; }

        [DataMember]
        public int? RequestID { get; set; }

        [DataMember]
        public DateTime RequestDate { get; set; }
        [DataMember]
        public int? AdmitID { get; set; }
        [DataMember]
        public int ReasonID { get; set; }
        public int SpecialityID { get; set; }

        [DataMember]
        public string RoomID { get; set; }

        [DataMember]
        public bool IsCpcTeam { get; set; }

        [DataMember]
        public int GpID { get; set; }
        [DataMember]
        public bool IsActive { get; set; }

        [DataMember]
        public bool IsDeleted { get; set; }

        [DataMember]
        public int InsertedBy { get; set; }

        [DataMember]
        public int UpdatedBy { get; set; }

        [DataMember]
        public DateTime InsertedAt { get; set; }

        [DataMember]
        public DateTime UpdatedAt { get; set; }

        [DataMember]
        public int TotalRecords { get; set; }

        [DataMember]
        public int PracticeID { get; set; }
        [DataMember]
        public int UserLoggingID { get; set; }
        [DataMember]
        public int ReferralPatientID { get; set; }

        [DataMember]
        public int ContractServiceID { get; set; }
        [DataMember]
        public int TriageStatusID { get; set; }

        [DataMember]
        public int ShiftFirstStaffID { get; set; }
        [DataMember]
        public int ShiftSecondStaffID { get; set; }
        [DataMember]
        public int ShiftThirdStaffID { get; set; }
        public Int32 CurrentPage { get; set; }

        public Int32 PageSize { get; set; }
        public string LocalityTitle { get; set; }
        public string PatientName { get; set; }
        public string NHINumber { get; set; }
        public string AssignedDrID { get; set; }
        public int LocalityID { get; set; }
        public string DischargeDateFrom { get; set; }
        public string DischargeDateTo { get; set; }
        public int DischargeID { get; set; }
        public string IPUuserID { get; set; }
        public int RoomIDNew { get; set; }
        public EncryptedInt AppointmentID { get; set; }

        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationRequested { get; set; }
        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationAdmitForm { get; set; }
        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationReason { get; set; }
        [DataMember]
        public List<MHN.Entity.Profile.Provider> LstProviders { get; set; }
        [DataMember]
        public List<LocalityType> LstLocalityType { get; set; }
        public List<BedCategoryType> LstBedCategoryType { get; set; }
        public List<FundingType> LstFundingType { get; set; }
        public List<AdmissionStatus> LstAdmissionStatus { get; set; }
        public List<AdmissionSubStatus> LstAdmissionSubStatus { get; set; }
        public List<Entity.HLForms.ACC45> LstAccidents { get; set; }
        public List<PracticeLocations> LstLocation { get; set; }
        public List<string> LstAdmissionType { get; set; }
        public List<string> LstPatientManagement { get; set; }

        [DataMember]
        public List<MHN.Entity.Profile.Provider> LstShiftStaff { get; set; }

        public List<HomeLeave> LstHomeLeave { get; set; }

        public List<ReferralSpeciality> LstReferralSpeciality { get; set; }

        public string LodgerCarer { get; set; }
        public string Acuity { get; set; }
        public string SmokingStatus { get; set; }
        public string FamilyViolence { get; set; }

        public string PatientAdmitted { get; set; }

        public bool PrivacyStatementOne { get; set; }
        public bool PrivacyStatementTwo { get; set; }

        public bool VisitorFlag { get; set; }
        public bool ReligiousVisitors { get; set; }

        [DataMember]
        public int NoofGuest { get; set; }
        [DataMember]
        public string Comments { get; set; }
        [DataMember]
        public int? AssignedNurseID { get; set; }
        [DataMember]
        public string AssignedDoctorID { get; set; }
        [DataMember]
        public string AssignedNurse { get; set; }
        [DataMember]
        public string AssignedDoctor { get; set; }
  public string ReasonForNotScreeningName { get; set; }

        public DateTime EOCStartDate { get; set; }
        public int BedCategoryID { get; set; }
        public string AdmissionType { get; set; }
        public int ReferralStatusID { get; set; }
        public int FundingTypeID { get; set; }
        public string PatientManagement { get; set; }

        public int AdmissionStatusID { get; set; }
        public int AdmissionSubStatusID { get; set; }

        public int HomeLeaveID { get; set; }

        public bool IsValid { get; set; }
        public string ACC45ID { get; set; }

        public int LocationID { get; set; }
        public string Triage { get; set; }

        public int TriageID { get; set; }

        public List<TriageScore> LstTriageScore { get; set; }

        public List<string> LstAlcoholInvolved { get; set; }
        public string AlcoholInvolved { get; set; }
        public string FamilyViolenceScreened { get; set; }
        public string FVOutcome { get; set; }
        public List<SelectListItem> localityLookupDDL { get; set; }
        public string HomeLeaveReason { get; set; }

        public string AssignedDoctorTXT { get; set; }
        public string SpecialityTXT { get; set; }
        public string LocationTXT { get; set; }
        public string RoomTXT { get; set; }
        public string ReasonTXT { get; set; }
        public string AdmissionSubStatusTXT { get; set; }
        public string ACC45TXT { get; set; }
        public string SeenByDoctor { get; set; }
      
         public string compliant { get; set; }
        public int IPUAriivalMethodID { get; set; }
        public string SeenByDoctorName { get; set; }
        public DateTime TimeSeen { get; set; }
        public string TimeSeenstring { get; set; }
        public DateTime Seendate { get; set; }
        // public int DischargeID { get; set; }
        public int ReasonForNotScreeningID { get; set; }
        public List<FamilyVoilanceList> FamilyVoilanceList { get; set; }
        public List<IpuArrivalMethod> IpuArrivalMethodList { get; set; }
        [DataMember]
        public EncryptedInt LetterDocumentIDEnc { get; set; }
        [DataMember]
        public EncryptedInt DischargeIDEnc { get; set; }

    }
    public class FamilyVoilanceList
    {
        public string ReasonForNotScreeningName { get; set; }
        public int ReasonForNotScreeningID { get; set; }

    }
    public class IpuArrivalMethod
    {
        public string ArrivalMethod { get; set; }
        public int IPUAriivalMethodID { get; set; }

    }
    [DataContract]
    public class IPUEnumeration
    {
        [DataMember]
        public int IPUEnumerationID { get; set; }
        [DataMember]
        public string Description { get; set; }
        [DataMember]
        public int ParentID { get; set; }
    }

    [DataContract]
    public class LocalityType
    {
        [DataMember]
        public int LocalityID { get; set; }
        [DataMember]
        public string LocalityTitle { get; set; }
    }
    public class BedCategoryType
    {
        public int BedCategoryID { get; set; }
        public string BedCategoryName { get; set; }
    }
    public class FundingType
    {
        public int FundingTypeID { get; set; }
        public string FundingTypeName { get; set; }
    }

    public class HomeLeave
    {
        public int FromTab { get; set; }
        public int HomeLeaveID { get; set; }
        public int PatientID { get; set; }
        public int PatientAdmissionID { get; set; }
        public int TypeID { get; set; }
        public string TypeName { get; set; }
        public DateTime LeaveDate { get; set; }
        public DateTime LeaveTime { get; set; }
        public DateTime ExpectedReturnDate { get; set; }
        public DateTime ExpectedReturnTime { get; set; }
        public DateTime ActualReturnDate { get; set; }
        public DateTime ActualReturnTime { get; set; }
        public List<HomeLeave> LstHomeLeave { get; set; }
        public bool IsValid { get; set; }
        public int UserLoggingID { get; set; }
        public int InsertedBy { get; set; }
    }
    public class AdmissionStatus
    {
        public int AdmissionStatusID { get; set; }
        public string AdmissionStatusName { get; set; }
    }
    public class AdmissionSubStatus
    {
        public int AdmissionSubStatusID { get; set; }
        public string AdmissionSubStatusName { get; set; }
    }
    public class IPUDischargeForm
    {
        public string ReasonForNotScreeningName { get; set; }
        public int LetterDocumentID { get; set; }
        public List<IPUDischargeForm> Objlist { get; set; }
        public string PatientName { get; set; }
        public string Triage { get; set; }
        public DateTime TimeSeen { get; set; }
        public string TimeSeenstring { get; set; }
        public DateTime Seendate { get; set; }
        public string DischargeBy { get; set; }
        public string AssignedDoctor { get; set; }
        public string IPUcurrentUser { get; set; }
        public string SeenByDoctor { get; set; }
        public string SeenByDoctorName { get; set; }
        public DateTime AdmissionDate { get; set; }
        public DateTime AdmissionTime { get; set; }

        public string NHINumber { get; set; }

        public string AdmissionTimestring { get; set; }

        public int FromTab { get; set; }
        public string PatientID { get; set; }
        public string PatientIDCon { get; set; }

        public int PracticeID { get; set; }
        public int PracticeLocationID { get; set; }
        public int UserLoggingID { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public int DischargeID { get; set; }
        public EncryptedInt EncDischargeID { get; set; }
        public DateTime DischargeDate { get; set; }
        public string DischargeDateFrom { get; set; }
        public string DischargeDateTo { get; set; }
        public int AppointmentID { get; set; }
        public int DischargeReasonID { get; set; }
        public string EOCEndDate { get; set; }
        public int EpisodeEndModeID { get; set; }
        public string EpisodeEndAccommodationIDS { get; set; }
        public int EpisodeEndAccommodationID { get; set; }
        public int EpisodeEndSupportID { get; set; }
        public int DischargeConvenienceID { get; set; }
        public DateTime MedicalDischargeDate { get; set; }
        public DateTime PhysicalDischargeDate { get; set; }
        public int DischargeDelayReasonID { get; set; }
        public List<DischargeReason> lstDischargeReason { get; set; }
        public List<EpisodeEndMode> lstEpisodeEndMode { get; set; }
        public List<EpisodeEndAccomadation> lstEpisodeEndAccommodation { get; set; }
        public List<EpisodeEndSupport> lstEpisodeEndSupport { get; set; }
        public List<DischargeConvenience> lstDischargeConvenience { get; set; }
        public List<DischargeDelayReason> lstDischargeDelayReason { get; set; }
        public bool IsValid { get; set; }
        public int LocalityID { get; set; }
        public string LocalityTitle { get; set; }
        public int PatientAdmissionID { get; set; }
        public int IsViewed { get; set; }
        public string DischargingCarer { get; set; }
        public DateTime MedicalDischargeTime { get; set; }
        public string MedicalDischargeTimestring { get; set; }
        public DateTime PhysicalDischargeTime { get; set; }

        public List<string> LstOutcome { get; set; }
        public string Outcome { get; set; }
        public string DiagnosisID { get; set; }
        public int iPMUniqueID { get; set; }
        public List<MHN.Entity.Profile.Provider> LstProviders { get; set; }
        public string AssignedDoctorID { get; set; }
        public string DischargeDelayReason { get; set; }
        public string Deschargedestination { get; set; }
        public string DischargevechalType { get; set; }
        public string Outcometext { get; set; }
        public string ConceptId { get; set; }
        public string DiseaseName { get; set; }
        public string FSN { get; set; }
        public string DischargeCarrerID { get; set; }
        public string AssignedDrID { get; set; }
        public string DischargeStatus { get; set; }
        public string HLStatus { get; set; }

        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalRecords { get; set; }
    }
    public class DischargeReason
    {
        public int DischargeReasonID { get; set; }
        public string DischargeReasonName { get; set; }
    }
    public class EpisodeEndMode
    {
        public int EpisodeEndModeID { get; set; }
        public string EpisodeEndModeName { get; set; }
    }
    public class EpisodeEndAccomadation
    {
        public int EpisodeEndAccomadationID { get; set; }
        public string EpisodeEndAccomadationName { get; set; }
    }
    public class EpisodeEndSupport
    {
        public int EpisodeEndSupportID { get; set; }
        public string EpisodeEndSupportName { get; set; }
    }
    public class DischargeConvenience
    {
        public int DischargeConvenienceID { get; set; }
        public string DischargeConvenienceName { get; set; }
    }
    public class IPUcontrollerVariable
    {

        public int page { get; set; }
        public int pageSize { get; set; }
        public bool MyProperty { get; set; }
        public EncryptedInt AppointmentID { get; set; }
        public EncryptedInt PatientID { get; set; }
        public Boolean IncludeInActive { get; set; }
        public bool IsFromReferral { get; set; }
        public int AdmissionFormID { get; set; }
        public int ReferralPatientID { get; set; }  
        public int localityId { get; set; }
        public bool IsPrintClicked { get; set; }
        public int PatientAdmissionID { get; set; }
        public int PracticeID { get; set; }
        public int PracticeLocationID { get; set; }
        public int UserLoggingID { get; set; }

    }
    public class DischargeDelayReason
    {
        public int DischargeDelayReasonID { get; set; }
        public string DischargeDelayReasonName { get; set; }
    }
    [DataContract]
    public class IPUAdmissionForm_Hospice
    {
        [DataMember]
        public int AdmissionFormID { get; set; }
        [DataMember]
        public int PatientAdmissionID { get; set; }
        [DataMember]
        public EncryptedInt  PatientID { get; set; }
        [DataMember]
        public DateTime AdmissionDate { get; set; }
        [DataMember]
        public string AdmissionTime { get; set; }

        public string AdmissionDateString { get; set; }

        [DataMember]
        public bool IsPreviousAdmission { get; set; }

        [DataMember]
        public int? RequestID { get; set; }

        [DataMember]
        public DateTime RequestDate { get; set; }
        [DataMember]
        public int? AdmitID { get; set; }
        [DataMember]
        public int ReasonID { get; set; }
        [DataMember]
        public int RoomID { get; set; }

        [DataMember]
        public bool IsCpcTeam { get; set; }

        [DataMember]
        public int GpID { get; set; }
        [DataMember]
        public bool IsActive { get; set; }

        [DataMember]
        public bool IsDeleted { get; set; }

        [DataMember]
        public int InsertedBy { get; set; }

        [DataMember]
        public int UpdatedBy { get; set; }

        [DataMember]
        public DateTime InsertedAt { get; set; }

        [DataMember]
        public DateTime UpdatedAt { get; set; }

        [DataMember]
        public int TotalRecords { get; set; }

        [DataMember]
        public int PracticeID { get; set; }
        [DataMember]
        public int UserLoggingID { get; set; }
        [DataMember]
        public EncryptedInt ReferralPatientID { get; set; }

        [DataMember]
        public int ContractServiceID { get; set; }
        [DataMember]
        public int TriageStatusID { get; set; }

        [DataMember]
        public int ShiftFirstStaffID { get; set; }
        [DataMember]
        public int ShiftSecondStaffID { get; set; }
        [DataMember]
        public int ShiftThirdStaffID { get; set; }

        public string LocalityTitle { get; set; }

        public int RoomIDNew { get; set; }


        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationRequested { get; set; }
        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationAdmitForm { get; set; }
        [DataMember]
        public List<IPUEnumeration> LstIPUEnumerationReason { get; set; }
        [DataMember]
        public List<MHN.Entity.Profile.Provider_Hospice> LstProviders { get; set; }
        [DataMember]
        public List<LocalityType> LstLocalityType { get; set; }

        [DataMember]
        public List<MHN.Entity.Profile.Provider_Hospice> LstShiftStaff { get; set; }

        [DataMember]
        public int NoofGuest { get; set; }
        [DataMember]
        public string Comments { get; set; }
        [DataMember]
        public int? AssignedNurseID { get; set; }
        [DataMember]
        public int? AssignedDoctorID { get; set; }
        [DataMember]
        public string AssignedNurse { get; set; }
        [DataMember]
        public string AssignedDoctor { get; set; }

        public DateTime EOCStartDate { get; set; }
    }
    [DataContract]
    public class ViewAuditIPUAdmissionForm
    {
        public int OldAssignedNurseID { get; set; }
        public int NewAssignedNurseID { get; set; }
        public string OldNurse { get; set; }
        public string NewNurse { get; set; }


        public int OldAssignedDoctorID { get; set; }
        public int NewAssignedDoctorID { get; set; }
        public string OldDoctor { get; set; }
        public string NewDoctor { get; set; }

        public DateTime InsertedAt { get; set; }
        public string FullName { get; set; }
    }
}
