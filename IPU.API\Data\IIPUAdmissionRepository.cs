using IPU.API.Models;

namespace IPU.API.Data
{
    /// <summary>
    /// Repository interface for IPU admission operations
    /// </summary>
    public interface IIPUAdmissionRepository
    {
        /// <summary>
        /// Search for patients by name or NHI number
        /// </summary>
        /// <param name="request">Search criteria</param>
        /// <returns>List of matching patients</returns>
        Task<PatientSearchResponse> SearchPatientsAsync(PatientSearchRequest request);

        /// <summary>
        /// Get patient by exact name match
        /// </summary>
        /// <param name="patientName">Patient full name</param>
        /// <returns>Patient information if found</returns>
        Task<PatientInfo?> GetPatientByNameAsync(string patientName);

        /// <summary>
        /// Get patient by NHI number
        /// </summary>
        /// <param name="nhiNumber">NHI number</param>
        /// <returns>Patient information if found</returns>
        Task<PatientInfo?> GetPatientByNHIAsync(string nhiNumber);

        /// <summary>
        /// Create a new IPU admission
        /// </summary>
        /// <param name="request">Admission details</param>
        /// <returns>Admission result</returns>
        Task<IPUAdmissionResponse> CreateAdmissionAsync(IPUAdmissionRequest request);

        /// <summary>
        /// Get all dropdown data needed for IPU admission form
        /// </summary>
        /// <returns>Comprehensive dropdown data</returns>
        Task<IPUAdmissionDropdownData> GetAdmissionDropdownDataAsync();

        /// <summary>
        /// Get admission reasons
        /// </summary>
        /// <returns>List of admission reasons</returns>
        Task<List<IPUEnumeration>> GetAdmissionReasonsAsync();

        /// <summary>
        /// Get available doctors/providers
        /// </summary>
        /// <returns>List of doctors</returns>
        Task<List<Provider>> GetDoctorsAsync();

        /// <summary>
        /// Get admission sources
        /// </summary>
        /// <returns>List of admission sources</returns>
        Task<List<AdmissionSubStatus>> GetAdmissionSourcesAsync();

        /// <summary>
        /// Get specialities
        /// </summary>
        /// <returns>List of specialities</returns>
        Task<List<ReferralSpeciality>> GetSpecialitiesAsync();

        /// <summary>
        /// Get practice locations
        /// </summary>
        /// <returns>List of locations</returns>
        Task<List<PracticeLocation>> GetLocationsAsync();

        /// <summary>
        /// Get available rooms for a specific location
        /// </summary>
        /// <param name="locationId">Location ID</param>
        /// <returns>List of available rooms</returns>
        Task<List<DropdownItem>> GetAvailableRoomsAsync(int? locationId = null);

        /// <summary>
        /// Get triage scores
        /// </summary>
        /// <returns>List of triage scores</returns>
        Task<List<TriageScore>> GetTriageScoresAsync();

        /// <summary>
        /// Get IPU arrival methods
        /// </summary>
        /// <returns>List of arrival methods</returns>
        Task<List<IpuArrivalMethod>> GetArrivalMethodsAsync();

        /// <summary>
        /// Get family violence screening reasons
        /// </summary>
        /// <returns>List of screening reasons</returns>
        Task<List<FamilyViolenceReason>> GetFamilyViolenceReasonsAsync();

        /// <summary>
        /// Get ACC45 numbers for a patient
        /// </summary>
        /// <param name="patientId">Patient ID</param>
        /// <returns>List of ACC45 numbers</returns>
        Task<List<ACC45>> GetACC45NumbersAsync(int patientId);
    }
}
