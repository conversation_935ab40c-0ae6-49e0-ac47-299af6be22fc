using IPU.API.Data;
using IPU.API.DTOs;
using IPU.API.Models;
using Microsoft.AspNetCore.Mvc;

namespace IPU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PatientController : ControllerBase
    {
        private readonly IPatientRepository _patientRepository;
        private readonly ILogger<PatientController> _logger;

        public PatientController(IPatientRepository patientRepository, ILogger<PatientController> logger)
        {
            _patientRepository = patientRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get total number of inpatients (patients with IsAdmitted = 1)
        /// </summary>
        [HttpGet("inpatients/count")]
        [ProducesResponseType(typeof(ApiResponse<int>), 200)]
        public async Task<ActionResult<ApiResponse<int>>> GetTotalInpatients()
        {
            try
            {
                var count = await _patientRepository.GetTotalInpatientsAsync();
                return Ok(ApiResponse<int>.SuccessResponse(count, $"Total inpatients: {count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total inpatients count");
                return StatusCode(500, ApiResponse<int>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get number of patients admitted in IPU (IsAdmitted = 1 and has record in tblPatientAdmission)
        /// </summary>
        [HttpGet("admitted/count")]
        [ProducesResponseType(typeof(ApiResponse<int>), 200)]
        public async Task<ActionResult<ApiResponse<int>>> GetAdmittedPatientsCount()
        {
            try
            {
                var count = await _patientRepository.GetAdmittedPatientsCountAsync();
                return Ok(ApiResponse<int>.SuccessResponse(count, $"Admitted patients in IPU: {count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admitted patients count");
                return StatusCode(500, ApiResponse<int>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Check if a patient is admitted in the IPU by name
        /// </summary>
        [HttpGet("admission-status/{patientName}")]
        [ProducesResponseType(typeof(ApiResponse<PatientAdmissionStatusDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<PatientAdmissionStatusDto>), 404)]
        public async Task<ActionResult<ApiResponse<PatientAdmissionStatusDto>>> CheckPatientAdmissionStatus(string patientName)
        {
            try
            {
                var status = await _patientRepository.CheckPatientAdmissionStatusAsync(patientName);
                
                if (status.PatientName == patientName && status.Message != "Patient not found")
                {
                    return Ok(ApiResponse<PatientAdmissionStatusDto>.SuccessResponse(status, status.Message));
                }

                return NotFound(ApiResponse<PatientAdmissionStatusDto>.ErrorResponse($"Patient '{patientName}' not found"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking patient admission status for {PatientName}", patientName);
                return StatusCode(500, ApiResponse<PatientAdmissionStatusDto>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get list of discharged patients by date
        /// </summary>
        [HttpGet("discharged")]
        [ProducesResponseType(typeof(ApiResponse<List<DischargeDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<DischargeDto>>>> GetDischargedPatientsByDate([FromQuery] DateTime? date = null)
        {
            try
            {
                var searchDate = date ?? DateTime.Today;
                var dischargedPatients = await _patientRepository.GetDischargedPatientsByDateAsync(searchDate);
                
                var message = date.HasValue 
                    ? $"Discharged patients on {searchDate:yyyy-MM-dd}: {dischargedPatients.Count}"
                    : $"Discharged patients today: {dischargedPatients.Count}";

                return Ok(ApiResponse<List<DischargeDto>>.SuccessResponse(dischargedPatients, message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discharged patients by date");
                return StatusCode(500, ApiResponse<List<DischargeDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get list of admitted patients by date
        /// </summary>
        [HttpGet("admitted")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientAdmissionDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<PatientAdmissionDto>>>> GetAdmittedPatientsByDate([FromQuery] DateTime? date = null)
        {
            try
            {
                var searchDate = date ?? DateTime.Today;
                var admittedPatients = await _patientRepository.GetAdmittedPatientsByDateAsync(searchDate);
                
                var message = date.HasValue 
                    ? $"Admitted patients on {searchDate:yyyy-MM-dd}: {admittedPatients.Count}"
                    : $"Admitted patients today: {admittedPatients.Count}";

                return Ok(ApiResponse<List<PatientAdmissionDto>>.SuccessResponse(admittedPatients, message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admitted patients by date");
                return StatusCode(500, ApiResponse<List<PatientAdmissionDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get list of medically discharged patients (IsAdmitted = 1, IsParked = 1, IsActive = 1)
        /// </summary>
        [HttpGet("medically-discharged")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientAdmissionDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<PatientAdmissionDto>>>> GetMedicallyDischargedPatients()
        {
            try
            {
                var medicallyDischargedPatients = await _patientRepository.GetMedicallyDischargedPatientsAsync();
                return Ok(ApiResponse<List<PatientAdmissionDto>>.SuccessResponse(
                    medicallyDischargedPatients, 
                    $"Medically discharged patients: {medicallyDischargedPatients.Count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting medically discharged patients");
                return StatusCode(500, ApiResponse<List<PatientAdmissionDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get room allocation for patients admitted today
        /// </summary>
        [HttpGet("today-admitted/room-allocation")]
        [ProducesResponseType(typeof(ApiResponse<List<RoomAllocationDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<RoomAllocationDto>>>> GetTodayAdmittedPatientsRoomAllocation()
        {
            try
            {
                var roomAllocations = await _patientRepository.GetTodayAdmittedPatientsRoomAllocationAsync();
                return Ok(ApiResponse<List<RoomAllocationDto>>.SuccessResponse(
                    roomAllocations, 
                    $"Room allocations for today's admissions: {roomAllocations.Count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's admitted patients room allocation");
                return StatusCode(500, ApiResponse<List<RoomAllocationDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get list of available rooms (IsOccupied = 0)
        /// </summary>
        [HttpGet("rooms/available")]
        [ProducesResponseType(typeof(ApiResponse<List<AvailableRoomDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<AvailableRoomDto>>>> GetAvailableRooms()
        {
            try
            {
                var availableRooms = await _patientRepository.GetAvailableRoomsAsync();
                return Ok(ApiResponse<List<AvailableRoomDto>>.SuccessResponse(
                    availableRooms, 
                    $"Available rooms: {availableRooms.Count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available rooms");
                return StatusCode(500, ApiResponse<List<AvailableRoomDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Update patient room allocation (room must be available - IsOccupied = 0)
        /// </summary>
        [HttpPut("admission/{admissionId}/room")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
        public async Task<ActionResult<ApiResponse<bool>>> UpdatePatientRoomAllocation(
            int admissionId, 
            [FromBody] UpdateRoomAllocationDto updateDto)
        {
            try
            {
                // Check if admission exists
                var admission = await _patientRepository.GetPatientAdmissionByIdAsync(admissionId);
                if (admission == null)
                {
                    return NotFound(ApiResponse<bool>.ErrorResponse("Patient admission not found"));
                }

                // Check if new room is available
                var availableRooms = await _patientRepository.GetAvailableRoomsAsync();
                var targetRoom = availableRooms.FirstOrDefault(r => r.LocalityID == updateDto.NewRoomID);
                
                if (targetRoom == null)
                {
                    return BadRequest(ApiResponse<bool>.ErrorResponse("Room is not available or does not exist"));
                }

                var success = await _patientRepository.UpdatePatientRoomAllocationAsync(
                    admissionId, 
                    updateDto.NewRoomID, 
                    updateDto.Comments);

                if (success)
                {
                    return Ok(ApiResponse<bool>.SuccessResponse(true, 
                        $"Patient room updated to {targetRoom.LocalityTitle} successfully"));
                }

                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Failed to update patient room allocation"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating patient room allocation for admission {AdmissionId}", admissionId);
                return StatusCode(500, ApiResponse<bool>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get all active admissions
        /// </summary>
        [HttpGet("admissions/active")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientAdmissionDto>>), 200)]
        public async Task<ActionResult<ApiResponse<List<PatientAdmissionDto>>>> GetActiveAdmissions()
        {
            try
            {
                var activeAdmissions = await _patientRepository.GetActiveAdmissionsAsync();
                return Ok(ApiResponse<List<PatientAdmissionDto>>.SuccessResponse(
                    activeAdmissions, 
                    $"Active admissions: {activeAdmissions.Count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active admissions");
                return StatusCode(500, ApiResponse<List<PatientAdmissionDto>>.ErrorResponse("Internal server error"));
            }
        }

        /// <summary>
        /// Get patient statistics summary
        /// </summary>
        [HttpGet("statistics")]
        [ProducesResponseType(typeof(ApiResponse<PatientStatsDto>), 200)]
        public async Task<ActionResult<ApiResponse<PatientStatsDto>>> GetPatientStatistics()
        {
            try
            {
                var totalInpatients = await _patientRepository.GetTotalInpatientsAsync();
                var admittedPatients = await _patientRepository.GetAdmittedPatientsCountAsync();
                var dischargedToday = (await _patientRepository.GetTodayDischargedPatientsAsync()).Count;
                var admittedToday = (await _patientRepository.GetAdmittedPatientsByDateAsync(DateTime.Today)).Count;
                var medicallyDischarged = (await _patientRepository.GetMedicallyDischargedPatientsAsync()).Count;

                var stats = new PatientStatsDto
                {
                    TotalInpatients = totalInpatients,
                    AdmittedPatients = admittedPatients,
                    DischargedToday = dischargedToday,
                    AdmittedToday = admittedToday,
                    MedicallyDischargedPatients = medicallyDischarged,
                    GeneratedAt = DateTime.Now
                };

                return Ok(ApiResponse<PatientStatsDto>.SuccessResponse(stats, "Patient statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting patient statistics");
                return StatusCode(500, ApiResponse<PatientStatsDto>.ErrorResponse("Internal server error"));
            }
        }
    }
}
