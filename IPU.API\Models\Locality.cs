namespace IPU.API.Models
{
    /// <summary>
    /// Represents a locality (Building, Ward, Room, or Bed) from [IPU].[tblLocality] table
    /// </summary>
    public class Locality
    {
        public int LocalityID { get; set; }
        public string LocalityTitle { get; set; } = string.Empty;
        public string LocalityCode { get; set; } = string.Empty;
        public string? LocalityDescription { get; set; }
        public int LocalityTypeID { get; set; }
        public int? ParentLocalityID { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public int InsertedBy { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime InsertedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public long UserLoggingID { get; set; }
        public byte[] AnalysisTimestamp { get; set; } = Array.Empty<byte>();
        public int? PracticeID { get; set; }
        public int? PracticeLocationID { get; set; }
        public int? SequenceNo { get; set; }
        public bool IsOccupied { get; set; }
        public string? Facilities { get; set; }
        public bool? IsMultipleRoom { get; set; }
        public bool? IsHomeLeave { get; set; }

        // Navigation property
        public LocalityType? LocalityType { get; set; }
        
        // Helper properties for easier access
        public string TypeName => LocalityType?.LocalityTypeName ?? string.Empty;
        public string? ParentTitle { get; set; } // For displaying parent locality name
    }
}
